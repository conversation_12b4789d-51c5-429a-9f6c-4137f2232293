import os

import setuptools  # type: ignore

package_root = os.path.abspath(os.path.dirname(__file__))

name = "protos-python"
description = "Cloudbeds protos library"

version = {}
with open(os.path.join(package_root, "VERSION.txt")) as fp:
    version = fp.read()

dependencies = [
    "grpcio>=1.70.0, <2.0.0",
    "protobuf>=5.28.0, <6.0.0",
    "grpcio-tools>=1.70.0, <2.0.0",
    "googleapis-common-protos>=1.66.0"
]

url = "https://github.com/cloudbeds/protos-python"

packages = [
    package
    for package in setuptools.PEP420PackageFinder.find()
    if package.startswith("cloudbeds")
]

namespaces = ["cloudbeds"]

setuptools.setup(
    name=name,
    version=version,
    description=description,
    author="Cloudbeds",
    license="Proprietary",
    url=url,
    classifiers=[
        "Intended Audience :: Developers",
        "Programming Language :: Python",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Operating System :: OS Independent",
        "Topic :: Internet",
    ],
    platforms="Posix; MacOS X; Windows",
    packages=packages,
    python_requires=">=3.7",
    namespace_packages=namespaces,
    install_requires=dependencies,
    include_package_data=True,
    zip_safe=False,
)
