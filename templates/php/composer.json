{"name": "cloudbeds/protos-php", "description": "gRPC and protobuf definitions at Cloudbeds.", "require": {"php": ">=7.4", "grpc/grpc": "1.42.*", "google/common-protos": "^4.3"}, "suggest": {"ext-grpc": "Enables use of gRPC, a universal high-performance RPC framework created by Google.", "ext-protobuf": "Provides a significant increase in throughput over the pure PHP protobuf implementation. See https://cloud.google.com/php/grpc for installation instructions.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "autoload": {"psr-4": {"Cloudbeds\\Protos\\": ""}}}