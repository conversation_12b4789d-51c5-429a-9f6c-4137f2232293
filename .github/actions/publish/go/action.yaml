name: 'Publish Go artifacts'
description: Action to publish Go sources

inputs:
  github-token:
    description: 'The token to used to clone and interact with Github repositories.'
    required: true
  cicd-user-name:
    description: 'The name of the user used to create any commits / tags.'
    default: 'cloudbeds-ci'
    required: false
  cicd-user-email:
    description: 'The user of the user used to create any commits.'
    default: '<EMAIL>'
    required: false
  buf-version:
    description: 'The version of buf to install'
    required: true

runs:
  using: composite
  steps:
    - name: checkout go protos
      uses: actions/checkout@v4
      with:
        repository: cloudbeds/protos-go
        path: protos-go
        token: ${{ inputs.github-token }}
        fetch-depth: 0

    - uses: actions/setup-go@v3
      with:
        go-version: '1.19.1'

    - uses: dorny/paths-filter@v3
      id: changes
      with:
        token: ${{ inputs.github-token }}
        list-files: 'csv'
        filters: |
          protos:
            - 'cloudbeds/**'
          version:
            - 'VERSION.txt'
            - 'cloudbeds/**/VERSION.txt'

    - name: Setup buf
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      uses: bufbuild/buf-setup-action@v1.50.0
      with:
        github_token: ${{ inputs.github-token }}

    - name: Generate sources
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      run: |
        buf generate

    - name: Publish artifacts to protos-go
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      env:
        GOPRIVATE: github.com/cloudbeds/*
      run: |
        VERSIONS_CHANGED="${{ steps.changes.outputs.version_files }}"
        echo "Version changes ${VERSIONS_CHANGED}"

        VERSION=$(<VERSION.txt)
        echo "Releasing go artifacts for version $VERSION"
        SRC_REPO=./
        DEST_REPO=./protos-go

        # Repo should exist and at a minimum should have a v0.0.0 tag
        # git tag -a v0.1.0 -m "Initial version"
        # git push origin v0.1.0

        function parse_module_name() {
            # Strip trailing slash, if there is one
            MODULE_DIR="${d%/}"
            # Pop of the last path
            MODULE_NAME="${MODULE_DIR##*/}"

            echo "$MODULE_NAME"
        }

        git config --global user.name ${{ inputs.cicd-user-name }}
        git config --global user.email ${{ inputs.cicd-user-email }}
        git remote set-url origin https://x-access-token:${{ inputs.github-token }}@github.com/cloudbeds/protos-go
        git config --global url.https://x-access-token:${{ inputs.github-token }}@github.com/.insteadOf https://github.com/

        HAS_CHANGES=false

        MODULES_WITH_CHANGES=()

        pushd $DEST_REPO
        if [ ! -f "go.mod" ]; then
            echo "Initializing root go.mod"
            go mod init github.com/cloudbeds/protos-go
        fi
        popd

        for d in ./gen/go/cloudbeds/*/ ; do
            echo "${d%?}"
            rsync -av --delete --exclude 'go.mod' --exclude 'go.sum' "${d%?}" $DEST_REPO/

            MODULE_NAME="$(parse_module_name $d)"

            MODULE_DEPENDENCIES=()
            MODULE_DEPENDENCIES_FILE="${SRC_REPO}/cloudbeds/${MODULE_NAME}/deps.txt"

            echo "Checking for dependencies at ${MODULE_DEPENDENCIES_FILE}"
            if [ -f "${MODULE_DEPENDENCIES_FILE}" ]; then
              while read d; do
                MODULE_DEPENDENCIES+=("$d")
              done <"${MODULE_DEPENDENCIES_FILE}"

              echo "Found ${MODULE_DEPENDENCIES} deps for ${MODULE_NAME}"
            fi

            echo "Checking for proto changes for module ${MODULE_NAME}"

            MODULE_VERSION_CHANGED=false
            if echo "${VERSIONS_CHANGED}" | grep -q "cloudbeds/${MODULE_NAME}/VERSION.txt"; then
              echo "Detected cloudbeds/${MODULE_NAME}/VERSION.txt change"
              MODULE_VERSION_CHANGED=true
            fi

            pushd $DEST_REPO/$MODULE_NAME

            # Detect proto changes before running go mod init/tidy.
            git add -N .
            git diff -- .
            if ! git diff -- . --exit-code > /dev/null || $MODULE_VERSION_CHANGED; then
                HAS_CHANGES=true
                MODULES_WITH_CHANGES+=("${MODULE_NAME}")
            fi

            if ! $HAS_CHANGES || ! $MODULE_VERSION_CHANGED; then
                popd
                continue
            fi

            if [ ! -f "go.mod" ]; then
                echo "Initializing module $MODULE_NAME"
                go mod init github.com/cloudbeds/protos-go/$MODULE_NAME
            fi

            # Grab the latest version for any dependencies
            if (( ${#MODULE_DEPENDENCIES[@]} != 0 )); then
              # return to root
              popd

              for m in "${MODULE_DEPENDENCIES[@]}"
              do
                MODULE_VERSION_PATH="${SRC_REPO}/cloudbeds/${m}/VERSION.txt"

                echo "Reading module version from ${MODULE_VERSION_PATH}"
                MODULE_VERSION=$(<"${MODULE_VERSION_PATH}")

                echo "Requiring latest version of ${m} via 'go get github.com/cloudbeds/protos-go/${m}@${MODULE_VERSION}'"
                pushd $DEST_REPO/$MODULE_NAME
                go get github.com/cloudbeds/protos-go/${m}@${MODULE_VERSION}
                popd
              done

              # return to module dir for "go mod tidy"
              pushd $DEST_REPO/$MODULE_NAME
            fi

            echo "Running mod tidy for $MODULE_NAME"
            go mod tidy

            popd
        done

        DEV_BRANCH="$GITHUB_REF_NAME-cicd"
        if [[ "$GITHUB_REF_NAME" != "master" ]]; then
            pushd $DEST_REPO
            # For non-master branches, create a branch on the protos-go remote and push changes there.
            if ! git rev-parse --verify ${DEV_BRANCH}; then
                echo "Creating branch ${DEV_BRANCH}"
                git branch ${DEV_BRANCH}
            fi

            git checkout ${DEV_BRANCH}

            git add .
            if ! git diff --cached --exit-code > /dev/null; then
              git commit -m "Update of protos ${VERSION}"
            fi

            if git ls-remote --heads --exit-code origin ${DEV_BRANCH}; then
                echo "Branch $DEV_BRANCH exists on remote, pulling latest changes"
                git fetch origin ${DEV_BRANCH}
                git merge -s recursive -X ours origin/${DEV_BRANCH}
            fi

            git push origin ${DEV_BRANCH}
            popd
        else
            # For master, we'll commit all changes to a ${VERSION} tag as well as a tag per module following the
            # module/${MODULE_VERSION} scheme.
            pushd $DEST_REPO

            git add .
            if ! git diff --cached --exit-code > /dev/null; then
              git commit -m "Release of protos ${VERSION}"
              git push origin HEAD
            fi

            git tag -a ${VERSION} -m "Release of protos ${VERSION}"
            git push origin ${VERSION}

            echo "Cleaning up ${DEV_BRANCH} branch"
            git push origin --delete ${DEV_BRANCH} 2>/dev/null || true

            popd

            # Push a specific version per module, following Go's versioning scheme:
            # See https://go.dev/doc/modules/managing-source#multiple-module-source
            for m in "${MODULES_WITH_CHANGES[@]}"
            do
                MODULE_VERSION_PATH="${SRC_REPO}/cloudbeds/${m}/VERSION.txt"

                echo "Reading module version from ${MODULE_VERSION_PATH}"
                MODULE_VERSION=$(<"${MODULE_VERSION_PATH}")
                MODULE_TAG="${m}/${MODULE_VERSION}"

                echo "Creating module tag release ${MODULE_TAG}"

                pushd $DEST_REPO
                git tag -a ${MODULE_TAG} -m "Release of ${MODULE_TAG}"
                git push origin ${MODULE_TAG}
                popd
            done
        fi
