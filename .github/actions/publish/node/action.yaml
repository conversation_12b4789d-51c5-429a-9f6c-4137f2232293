name: "Publish node artifacts"
description: Action to publish node JS sources

inputs:
  github-token:
    description: "The token to used to clone and interact with Github repositories."
    required: true
  npm-token:
    description: "The token to used to publish to npm."
    required: true
  cicd-user-name:
    description: "The name of the user used to create any commits / tags."
    default: "cloudbeds-ci"
    required: false
  cicd-user-email:
    description: "The user of the user used to create any commits."
    default: "<EMAIL>"
    required: false
  buf-version:
    description: 'The version of buf to install'
    required: true


runs:
  using: composite
  steps:
    - name: checkout node protos
      uses: actions/checkout@v4
      with:
        repository: cloudbeds/protos-node
        path: protos-node
        token: ${{ inputs.github-token }}
        fetch-depth: 0

    - uses: dorny/paths-filter@v3
      id: changes
      with:
        token: ${{ inputs.github-token }}
        list-files: "csv"
        filters: |
          protos:
            - 'cloudbeds/**'
          version:
            - 'VERSION.txt'
            - 'cloudbeds/**/VERSION.txt'

    - name: Setup buf
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      uses: bufbuild/buf-setup-action@v1.50.0
      with:
        github_token: ${{ inputs.github-token }}

    - name: Generate sources
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      run: |
        buf generate --include-imports

    - name: Publish artifacts to protos-node
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      env:
        NPM_TOKEN: ${{ inputs.npm-token }}
      run: |
        VERSION=$(<VERSION.txt)
        echo "Releasing node artifacts for version $VERSION"
        SRC_REPO=./
        DEST_REPO=./protos-node
        SHORT_SHA="${GITHUB_SHA:0:7}"

        # Repo should exist and at a minimum should have a v0.0.0 tag
        # git tag -a v0.1.0 -m "Initial version"
        # git push origin v0.1.0

        # transpile generated protos
        cp $SRC_REPO/templates/node/package.json $SRC_REPO/gen/node
        cp $SRC_REPO/templates/node/package-lock.json $SRC_REPO/gen/node
        cp $SRC_REPO/templates/node/tsconfig.json $SRC_REPO/gen/node

        pushd $SRC_REPO/gen/node
        npm ci
        npm run build

        popd

        # copy transpiled files to the destination repo
        rsync -av --delete $SRC_REPO/gen/node/dist/cloudbeds $DEST_REPO/
        rsync -av --delete $SRC_REPO/gen/node/dist/google $DEST_REPO/
        rsync -av --delete $SRC_REPO/gen/node/dist/v2 $DEST_REPO/
        cp $SRC_REPO/VERSION.txt $DEST_REPO/
        cp $SRC_REPO/templates/node/package.json $DEST_REPO/
        cp $SRC_REPO/templates/node/package-lock.json $DEST_REPO/
        cp $SRC_REPO/templates/node/tsconfig.json $DEST_REPO/

        git config --global user.name ${{ inputs.cicd-user-name }}
        git config --global user.email ${{ inputs.cicd-user-email }}
        git remote set-url origin https://x-access-token:${{ inputs.github-token }}@github.com/cloudbeds/protos-node

        pushd $DEST_REPO

        DEV_BRANCH="$GITHUB_REF_NAME-cicd"

        if [[ "$GITHUB_REF_NAME" != "master" ]]; then
          VERSION="${VERSION}-${SHORT_SHA}"
        fi

        if [[ -n $NPM_TOKEN ]]; then
          echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc;
          NPM_VERSION="${VERSION:1}" # remove leading v
          npm version $NPM_VERSION --allow-same-version --git-tag-version false

          if [[ "$GITHUB_REF_NAME" != "master" ]]; then
            npm publish --tag rc
          else
            npm publish
          fi

          rm .npmrc
        fi

        if [[ "$GITHUB_REF_NAME" != "master" ]]; then
            # For non-master branches, create a branch on the protos-node remote and push changes there.
            if ! git rev-parse --verify ${DEV_BRANCH}; then
                echo "Creating branch ${DEV_BRANCH}"
                git branch ${DEV_BRANCH}
            fi

            git checkout ${DEV_BRANCH}

            git add .
            if ! git diff --cached --exit-code > /dev/null; then
              git commit -m "Update of protos ${VERSION}"
            fi

            if git ls-remote --heads --exit-code origin ${DEV_BRANCH}; then
                echo "Branch $DEV_BRANCH exists on remote, pulling latest changes"
                git fetch origin ${DEV_BRANCH}
                git merge -s recursive -X ours origin/${DEV_BRANCH}
            fi

            git push origin ${DEV_BRANCH}
            popd
        else
            # For master, we'll commit all changes to a ${VERSION} tag
            git add .
            if ! git diff --cached --exit-code > /dev/null; then
              git commit -m "Release of protos ${VERSION}"
              git push origin HEAD
            fi

            git tag -a ${VERSION} -m "Release of protos ${VERSION}"
            git push origin ${VERSION}
            git push origin --delete ${DEV_BRANCH} 2>/dev/null || true

            popd
        fi
