name: 'Publish Python artifacts'
description: Action to publish Python sources

inputs:
  github-token:
    description: 'The token to used to clone and interact with Github repositories.'
    required: true
  cicd-user-name:
    description: 'The name of the user used to create any commits / tags.'
    default: 'cloudbeds-ci'
    required: false
  cicd-user-email:
    description: 'The user of the user used to create any commits.'
    default: '<EMAIL>'
    required: false
  buf-version:
    description: 'The version of buf to install'
    required: true

runs:
  using: composite
  steps:
    - name: checkout python protos
      uses: actions/checkout@v4
      with:
        repository: cloudbeds/protos-python
        path: protos-python
        token: ${{ inputs.github-token }}
        fetch-depth: 0

    - uses: dorny/paths-filter@v3
      id: changes
      with:
        token: ${{ inputs.github-token }}
        list-files: 'csv'
        filters: |
          protos:
            - 'cloudbeds/**'
          version:
            - 'VERSION.txt'
            - 'cloudbeds/**/VERSION.txt'

    - name: Setup buf
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      uses: bufbuild/buf-setup-action@v1.50.0
      with:
        github_token: ${{ inputs.github-token }}

    - name: Generate sources
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      run: |
        buf generate

    - name: Publish artifacts to protos-python
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      run: |
        VERSION=$(<VERSION.txt)
        echo "Releasing python artifacts for version $VERSION"
        SRC_REPO=./
        DEST_REPO=./protos-python
        SHORT_SHA="${GITHUB_SHA:0:7}"

        # Repo should exist and at a minimum should have a v0.0.0 tag
        # git tag -a v0.1.0 -m "Initial version"
        # git push origin v0.1.0

        # copy generated protos to the destination repo
        cp $SRC_REPO/VERSION.txt $DEST_REPO/
        rsync -av --delete $SRC_REPO/gen/python/cloudbeds $DEST_REPO/

        # copy other files to the destination repo
        cp $SRC_REPO/templates/python/setup.py $DEST_REPO/setup.py

        git config --global user.name ${{ inputs.cicd-user-name }}
        git config --global user.email ${{ inputs.cicd-user-email }}
        git remote set-url origin https://x-access-token:${{ inputs.github-token }}@github.com/cloudbeds/protos-python

        DEV_BRANCH="$GITHUB_REF_NAME-cicd"
        if [[ "$GITHUB_REF_NAME" != "master" ]]; then
            pushd $DEST_REPO
            # For non-master branches, create a branch on the protos-python remote and push changes there.
            if ! git rev-parse --verify ${DEV_BRANCH}; then
                echo "Creating branch ${DEV_BRANCH}"
                git branch ${DEV_BRANCH}
            fi

            git checkout ${DEV_BRANCH}

            git add .
            if ! git diff --cached --exit-code > /dev/null; then
              git commit -m "Update of protos ${VERSION}"
            fi

            if git ls-remote --heads --exit-code origin ${DEV_BRANCH}; then
                echo "Branch $DEV_BRANCH exists on remote, pulling latest changes"
                git fetch origin ${DEV_BRANCH}
                git merge -s recursive -X ours origin/${DEV_BRANCH}
            fi

            git push origin ${DEV_BRANCH}
            popd
        else
            # For master, we'll commit all changes to a ${VERSION} tag
            pushd $DEST_REPO

            git add .
            if ! git diff --cached --exit-code > /dev/null; then
              git commit -m "Release of protos ${VERSION}"
              git push origin HEAD
            fi

            git tag -a ${VERSION} -m "Release of protos ${VERSION}"
            git push origin ${VERSION}
            git push origin --delete ${DEV_BRANCH} 2>/dev/null || true

            popd
        fi
