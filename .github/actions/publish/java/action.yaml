name: 'Publish Java artifacts'
description: Action to publish Java sources and artifacts to GH Packages

inputs:
  github-token:
    description: 'The token to used to clone and interact with Github repositories.'
    required: true
  maven-token:
    description: 'The token to used to publish to maven.'
    required: true
  cicd-user-name:
    description: 'The name of the user used to create any commits / tags.'
    default: 'cloudbeds-ci'
    required: false
  cicd-user-email:
    description: 'The user of the user used to create any commits.'
    default: '<EMAIL>'
    required: false
  buf-version:
    description: 'The version of buf to install'
    required: true

runs:
  using: composite
  steps:
    - name: checkout java protos
      uses: actions/checkout@v4
      with:
        repository: cloudbeds/protos-java
        path: protos-java
        token: ${{ inputs.github-token }}
        fetch-depth: 0

    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: maven

    - uses: dorny/paths-filter@v3
      id: changes
      with:
        token: ${{ inputs.github-token }}
        list-files: 'csv'
        filters: |
          protos:
            - 'cloudbeds/**'
          version:
            - 'VERSION.txt'
            - 'cloudbeds/**/VERSION.txt'

    - name: Setup buf
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      uses: bufbuild/buf-setup-action@v1.50.0
      with:
        github_token: ${{ inputs.github-token }}

    - name: Generate sources
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      run: |
        buf generate

    - name: Publish artifacts to protos-java repo and GH packages for maven
      if: steps.changes.outputs.protos == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      run: |
        VERSION=$(<VERSION.txt)
        echo "Releasing java artifacts for version $VERSION"
        SRC_REPO=./
        DEST_REPO=./protos-java
        SHORT_SHA="${GITHUB_SHA:0:7}"

        # Repo should exist and at a minimum should have a v0.0.0 tag
        # git tag -a v0.1.0 -m "Initial version"
        # git push origin v0.1.0

        mkdir -p $DEST_REPO/src/main/java
        rsync -av --delete $SRC_REPO/gen/java/com/cloudbeds $DEST_REPO/src/main/java/com/
        cp $SRC_REPO/templates/java/pom.xml $DEST_REPO/pom.xml
        cp $SRC_REPO/templates/java/settings.xml $HOME/.m2/settings.xml

        pushd $DEST_REPO
        git config --global user.name ${{ inputs.cicd-user-name }}
        git config --global user.email ${{ inputs.cicd-user-email }}
        git remote set-url origin https://x-access-token:${{ inputs.github-token }}@github.com/cloudbeds/protos-java

        git add -N .
        if ! git diff --exit-code > /dev/null; then
            # If we are not on main, then append <branch>-SNAPSHOT to the last version and push. Skip commit to master.
            if [[ "$GITHUB_REF_NAME" != "master" ]]; then
                VERSION="${VERSION}-${SHORT_SHA}-SNAPSHOT"
            fi

            MAVEN_VERSION="${VERSION:1}" # remove leading v per maven
            echo "Deploying version ${MAVEN_VERSION}"


            mvn clean compile
            mvn versions:set -DnewVersion=${MAVEN_VERSION}
            mvn deploy -Dgithub.username=${{ inputs.cicd-user-email }} -Dgithub.token=${{ inputs.maven-token }} -Dmaven.resolver.transport=wagon

            if [[ "$GITHUB_REF_NAME" == "master" ]]; then
                echo "Pushing to HEAD"
                git add .
                git commit -m "Release of protos ${VERSION}"
                git push origin HEAD
                git tag ${VERSION}
                git push origin ${VERSION}
                git push origin --delete ${DEV_BRANCH} 2>/dev/null || true
            else
                DEV_BRANCH="$GITHUB_REF_NAME-cicd"
                echo "Pushing to develop branch"

                # For non-master branches, create a branch on the protos-php remote and push changes there.
                if ! git rev-parse --verify ${DEV_BRANCH}; then
                    echo "Creating branch ${DEV_BRANCH}"
                    git branch ${DEV_BRANCH}
                fi

                git checkout ${DEV_BRANCH}

                git add .
                if ! git diff --cached --exit-code > /dev/null; then
                  git commit -m "Update of protos $GITHUB_REF_NAME"
                fi

                if git ls-remote --heads --exit-code origin ${DEV_BRANCH}; then
                    echo "Branch $DEV_BRANCH exists on remote, pulling latest changes"
                    git fetch origin ${DEV_BRANCH}
                    git merge -s recursive -X ours origin/${DEV_BRANCH}
                fi

                git push origin ${DEV_BRANCH}
            fi
        else
            echo "No changes detected for $DEST_REPO"
        fi
        popd
