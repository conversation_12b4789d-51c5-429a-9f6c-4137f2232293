name: Pull Request

on:
  workflow_dispatch:
  pull_request:

env:
  BUF_VERSION: "v1.50.0"

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: checkout protos
        uses: actions/checkout@v4

      - name: Install the `buf` CLI
        uses: bufbuild/buf-setup-action@v1.50.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Lint Protobuf sources
        uses: bufbuild/buf-lint-action@v1

  detect-bc-break:
    name: Detect breaking changes
    runs-on: ubuntu-latest
    steps:
      - name: checkout protos
        uses: actions/checkout@v4

      - name: Install the `buf` CLI
        uses: bufbuild/buf-setup-action@v1.50.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Detect breaking changes
        uses: bufbuild/buf-breaking-action@v1
        with:
          against: 'https://github.com/cloudbeds/protos.git#branch=master'

  check-versions:
    name: Validate versioning
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            protos:
              - 'cloudbeds/!(rpc/**)/**'
            version:
              - 'VERSION.txt'

      - name: Check for versions
        run: |
          if [ ! -f "VERSION.txt" ]; then
            echo "Root version file missing"
            exit 1
          fi

          for d in ./cloudbeds/* ; do
              if [ "$d" = "./cloudbeds/rpc" ]; then
                continue
              fi

              if [ ! -f "$d/VERSION.txt" ]; then
                  echo "Missing version file for $d"
                  exit 1
              fi
          done

      - name: Catch missing version changes
        if: steps.changes.outputs.protos == 'true' && steps.changes.outputs.version == 'false'
        run: |
          exit 1
