name: Publish artifacts

on: push

env:
  GH_TOKEN_APP_ID: 1118430
  GH_TOKEN_AWS_ROLE_ARN: arn:aws:iam::048781935247:role/GH-APP-OIDC-CBLibGenerator
  GH_TOKEN_AWS_SSM: /github/app/CBLibGenerator/private-key

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: checkout protos
        uses: actions/checkout@v4

      - name: Install the `buf` CL<PERSON>
        uses: bufbuild/buf-setup-action@v1.50.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Lint Protobuf sources
        uses: bufbuild/buf-lint-action@v1

  publish-java:
    name: Publish java artifacts
    needs: lint
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: checkout protos
        uses: actions/checkout@v4

      - name: Get gh app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["protos-java"]
          app_id: ${{ env.GH_TOKEN_APP_ID }}
          aws_role_arn: ${{ env.GH_TOKEN_AWS_ROLE_ARN }}
          aws_ssm_param_name: ${{ env.GH_TOKEN_AWS_SSM }}

      - name: Publish Java artifacts
        uses: ./.github/actions/publish/java
        with:
          github-token: ${{ steps.gh-app-token.outputs.github-token }}
          maven-token: ${{ secrets.CB_CI_MAVEN_GH_PACKAGES_WRITE }}

  publish-go:
    name: Publish go artifacts
    needs: lint
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: checkout protos
        uses: actions/checkout@v4

      - name: Get gh app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["protos-go"]
          app_id: ${{ env.GH_TOKEN_APP_ID }}
          aws_role_arn: ${{ env.GH_TOKEN_AWS_ROLE_ARN }}
          aws_ssm_param_name: ${{ env.GH_TOKEN_AWS_SSM }}

      - name: Publish go artifacts
        uses: ./.github/actions/publish/go
        with:
          github-token: ${{ steps.gh-app-token.outputs.github-token }}

  publish-php:
    name: Publish PHP artifacts
    needs: lint
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: checkout protos
        uses: actions/checkout@v4

      - name: Get gh app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["protos-php"]
          app_id: ${{ env.GH_TOKEN_APP_ID }}
          aws_role_arn: ${{ env.GH_TOKEN_AWS_ROLE_ARN }}
          aws_ssm_param_name: ${{ env.GH_TOKEN_AWS_SSM }}

      - name: Publish PHP artifacts
        uses: ./.github/actions/publish/php
        with:
          github-token: ${{ steps.gh-app-token.outputs.github-token }}

  publish-python:
    name: Publish Python artifacts
    needs: lint
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: checkout protos
        uses: actions/checkout@v4

      - name: Get gh app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["protos-python"]
          app_id: ${{ env.GH_TOKEN_APP_ID }}
          aws_role_arn: ${{ env.GH_TOKEN_AWS_ROLE_ARN }}
          aws_ssm_param_name: ${{ env.GH_TOKEN_AWS_SSM }}

      - name: Publish Python artifacts
        uses: ./.github/actions/publish/python
        with:
          github-token: ${{ steps.gh-app-token.outputs.github-token }}

  publish-node:
    name: Publish Node JS artifacts
    needs: lint
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: checkout protos
        uses: actions/checkout@v4

      - name: Get gh app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["protos-node"]
          app_id: ${{ env.GH_TOKEN_APP_ID }}
          aws_role_arn: ${{ env.GH_TOKEN_AWS_ROLE_ARN }}
          aws_ssm_param_name: ${{ env.GH_TOKEN_AWS_SSM }}

      - name: Publish Node JS artifacts
        uses: ./.github/actions/publish/node
        with:
          github-token: ${{ steps.gh-app-token.outputs.github-token }}
          npm-token: ${{ secrets.NPM_TOKEN }}
