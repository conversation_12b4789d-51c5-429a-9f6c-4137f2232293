#!/bin/bash

# Pre-commit Git checks.
# Set up:
#  ln -s .githooks/pre-commit .git/hooks/pre-commit

# Constants.
BOLD="\033[1m"
UNSET="\033[0m"
WHITE="\033[97m"
RED="\033[91m"
BACK_MAGENTA="\033[45m"
BACK_BLUE="\033[44m"
BACK_RED="\033[41m"
BACK_GREEN="\033[42m"

# Methods.
function echo_error {
  ERR_MSG=$1
  HELP_MSG=$2
  echo -e "$BOLD $BACK_BLUE $WHITE Precommit:\t $BACK_RED Changes NOT committed. $UNSET"
  echo -e "$BOLD $BACK_BLUE $WHITE Precommit:\t $BACK_RED $WHITE $ERR_MSG $BACK_BLUE $HELP_MSG $UNSET"
}

function echo_status {
  STATUS_MSG=$1
  echo -e "$BOLD $BACK_BLUE $WHITE Precommit:\t $STATUS_MSG $UNSET"
}

function echo_success {
  echo -e "$BOLD $BACK_BLUE $WHITE Precommit:\t $BACK_GREEN $WHITE SUCCESS. $UNSET All checks passed!"
}

# Check only the staged files.
NUM_TOTAL_FILES_CHANGED=$(git diff --cached --name-only | wc -l)
NUM_PROTO_FILES_CHANGED=$(git diff --cached --name-only "*.proto" | wc -l)

if [ $NUM_TOTAL_FILES_CHANGED -le 0 ]
then
  echo_error "No new files to commit." ""
  exit 1
fi

# Check proto lint format.
if [ $NUM_PROTO_FILES_CHANGED -gt 0 ]
then
  echo_status "Running buf lint..."
  buf lint
  FORMAT_STATUS=$?
  if [ $FORMAT_STATUS != 0 ]
  then
    echo_error "Linting failed." "Please fix them and try again."
    exit 1
  fi
fi

echo_success