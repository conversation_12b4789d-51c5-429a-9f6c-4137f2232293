version: v1
managed:
  enabled: false
plugins:
  # ProtoBuffs
  # GO
  - plugin: buf.build/protocolbuffers/go:v1.36.0
    out: gen/go
    opt: paths=source_relative
  # JAVA
  # there is broken change in new version
  - plugin: buf.build/protocolbuffers/java:v25.3
    out: gen/java
  # PHP
  - plugin: buf.build/protocolbuffers/php:v30.1
    out: gen/php
  - plugin: buf.build/community/roadrunner-server-php-grpc:v4.8.4
    out: gen/php
  - plugin: buf.build/protocolbuffers/python:v29.3
    out: gen/python
  - plugin: buf.build/protocolbuffers/pyi:v29.3
    out: gen/python
  # gRPC
  # GO
  - plugin: buf.build/grpc/go
    out: gen/go
    opt:
      - paths=source_relative
      - require_unimplemented_servers=false
  # JAVA
  - plugin: buf.build/grpc/java:v1.70.0
    out: gen/java

  # PHP
  - plugin: buf.build/grpc/php
    out: gen/php
  - plugin: buf.build/grpc/python
    out: gen/python

  # Node
  - plugin: buf.build/connectrpc/es:v1.0.0
    opt: target=ts
    out: gen/node
  - plugin: buf.build/bufbuild/es:v1.3.1
    opt: target=ts
    out: gen/node
  - plugin: buf.build/bufbuild/es:v2.5.2
    opt:
      - target=ts
      - rewrite_imports=@bufbuild/protobuf:@bufbuild/protobufv2
      - rewrite_imports=@bufbuild/protobuf/codegenv2:@bufbuild/protobufv2/codegenv2
      - rewrite_imports=@bufbuild/protobuf/wkt:@bufbuild/protobufv2/wkt
    out: gen/node/v2
