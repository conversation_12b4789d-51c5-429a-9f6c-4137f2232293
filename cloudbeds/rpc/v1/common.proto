syntax = "proto3";

package cloudbeds.rpc.v1;

option go_package = "github.com/cloudbeds/protos-go/rpc/v1;rpc";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.cloudbeds.rpc.v1";
option objc_class_prefix = "RPC";
option php_namespace = "Cloudbeds\\Protos\\RPC\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\RPC\\V1\\GPBMetadata";


enum LogicalOperator {
  LOGICAL_OPERATOR_UNSPECIFIED = 0;
  LOGICAL_OPERATOR_AND = 1;
  LOGICAL_OPERATOR_OR = 2;
}

enum Direction {
  DIRECTION_UNSPECIFIED = 0;
  DIRECTION_ASC = 1;
  DIRECTION_DESC = 2;
}

enum ConditionOperator {
  CONDITION_OPERATOR_UNSPECIFIED = 0;
  CONDITION_OPERATOR_GREATER_THAN_OR_EQUAL = 1;
  CONDITION_OPERATOR_LESS_THAN_OR_EQUAL = 2;
  CONDITION_OPERATOR_GREATER_THAN = 3;
  CONDITION_OPERATOR_LESS_THAN = 4;
  CONDITION_OPERATOR_EQUAL = 5;
  CONDITION_OPERATOR_NOT_EQUAL = 6;
  CONDITION_OPERATOR_IN = 7;
  CONDITION_OPERATOR_NOT_IN = 8;
  CONDITION_OPERATOR_CONTAINS = 9;
  CONDITION_OPERATOR_NOT_CONTAINS = 10;
  CONDITION_OPERATOR_IS_NULL = 11;
  CONDITION_OPERATOR_IS_NOT_NULL = 12;
  CONDITION_OPERATOR_STARTS_WITH = 13;
  CONDITION_OPERATOR_NOT_START_WITH = 14;
  CONDITION_OPERATOR_ENDS_WITH = 15;
  CONDITION_OPERATOR_NOT_END_WITH = 16;
  CONDITION_OPERATOR_LIKE_CASE_INSENSITIVE = 17;
  CONDITION_OPERATOR_LIKE_IN_CASE_INSENSITIVE = 18;
}