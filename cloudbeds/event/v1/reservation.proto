syntax = "proto3";

package cloudbeds.event.v1;

option go_package = "github.com/cloudbeds/protos-go/event/v1;cloudbeds";
option java_package = "com.cloudbeds.event.v1";
option java_outer_classname = "ReservationEventProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Event\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Event\\V1\\GPBMetadata";

import "cloudbeds/type/v1/money.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/timeofday.proto";

// ENUMS
enum ReservationEventType {
  RESERVATION_EVENT_TYPE_UNSPECIFIED = 0;
  RESERVATION_EVENT_TYPE_CREATED = 1;
  RESERVATION_EVENT_TYPE_UPDATED = 2;
  RESERVATION_EVENT_TYPE_DELETED = 3;
  RESERVATION_EVENT_TYPE_STATUS_UPDATED = 4;
  RESERVATION_EVENT_TYPE_ROOM_CREATED = 5;
  RESERVATION_EVENT_TYPE_ROOM_UPDATED = 6;
  RESERVATION_EVENT_TYPE_ROOM_DELETED = 7;
  RESERVATION_EVENT_TYPE_ROOM_REVENUE_ADDED = 8;
  RESERVATION_EVENT_TYPE_ROUTE_TRANSACTIONS = 9;
  RESERVATION_EVENT_TYPE_ADDON_ADDED = 10;
  RESERVATION_EVENT_TYPE_ADDON_UPDATED = 11;
  RESERVATION_EVENT_TYPE_ADDON_DELETED = 12;
  RESERVATION_EVENT_TYPE_ITEM_ADDED = 13;
  RESERVATION_EVENT_TYPE_ADJUSTMENT_ADDED = 14;
  RESERVATION_EVENT_TYPE_TAX_ADDED = 15;
  RESERVATION_EVENT_TYPE_FEE_ADDED = 16;
  RESERVATION_EVENT_TYPE_NIGHT_AUDIT = 17;
  RESERVATION_EVENT_TYPE_CUSTOM_ITEM_ADDED = 18;
  RESERVATION_EVENT_TYPE_CUSTOM_ITEM_DELETED = 19;
  RESERVATION_EVENT_TYPE_VOID = 20;
  RESERVATION_EVENT_TYPE_ADDON_POSTED = 21;
  RESERVATION_EVENT_TYPE_ROUTE_PENDING_TRANSACTIONS = 22;
  RESERVATION_EVENT_TYPE_MOVE_PENDING_TRANSACTIONS = 23;
  RESERVATION_EVENT_TYPE_SPACE_RESERVATION_CREATED = 24;
  RESERVATION_EVENT_TYPE_PAYMENT_FLOW_COMPLETED = 25;
  RESERVATION_EVENT_TYPE_MOVE_TRANSACTIONS = 26;
  RESERVATION_EVENT_TYPE_ROOM_RATES_RESET = 27;
}

enum ReservationStatus {
  RESERVATION_STATUS_UNSPECIFIED = 0;
  RESERVATION_STATUS_CONFIRMED = 3;
  RESERVATION_STATUS_CANCELED = 4;
  RESERVATION_STATUS_CHECKED_IN = 5;
  RESERVATION_STATUS_CHECKED_OUT = 6;
  RESERVATION_STATUS_NOT_CONFIRMED = 7;
  RESERVATION_STATUS_NO_SHOW = 8;
  RESERVATION_STATUS_PAYPAL_INIT = 9;
  RESERVATION_STATUS_PENDING_PAYMENT = 10;
  RESERVATION_STATUS_IN_PROGRESS = 100; // maybe needs to be deleted
  RESERVATION_STATUS_CALL_TO_CONFIRM = 101; // maybe needs to be deleted
}

enum ReservationRoomStatus {
  RESERVATION_ROOM_STATUS_UNSPECIFIED = 0;
  RESERVATION_ROOM_STATUS_PENDING = 1;
  RESERVATION_ROOM_STATUS_CHECKED_IN = 2;
  RESERVATION_ROOM_STATUS_CHECKED_OUT = 3;
}

enum ReservationTaxKind {
  RESERVATION_TAX_KIND_UNSPECIFIED = 0;
  RESERVATION_TAX_KIND_FEE = 1;
}

enum ReservationRevenueEventType {
  RESERVATION_REVENUE_EVENT_TYPE_UNSPECIFIED = 0;
  RESERVATION_REVENUE_EVENT_TYPE_MANUAL = 1;
  RESERVATION_REVENUE_EVENT_TYPE_NO_SHOW = 2;
  RESERVATION_REVENUE_EVENT_TYPE_CANCELLATION = 3;
}

enum ReservationExternalRelationType {
  RESERVATION_EXTERNAL_RELATION_TYPE_UNSPECIFIED = 0;
  RESERVATION_EXTERNAL_RELATION_TYPE_ITEM = 1;
  RESERVATION_EXTERNAL_RELATION_TYPE_ADDON = 2;
  RESERVATION_EXTERNAL_RELATION_TYPE_ADJUSTMENT = 3;
  RESERVATION_EXTERNAL_RELATION_TYPE_EXCLUSIVE_TAX = 4;
  RESERVATION_EXTERNAL_RELATION_TYPE_EXCLUSIVE_FEE = 5;
  RESERVATION_EXTERNAL_RELATION_TYPE_ROOM_REVENUE = 6;
  RESERVATION_EXTERNAL_RELATION_TYPE_ITEM_POS = 7;
}

enum ReservationRoutedTransactionType {
  RESERVATION_ROUTED_TRANSACTION_TYPE_UNSPECIFIED = 0;
  RESERVATION_ROUTED_TRANSACTION_TYPE_RATE = 1;
  RESERVATION_ROUTED_TRANSACTION_TYPE_FEE = 2;
  RESERVATION_ROUTED_TRANSACTION_TYPE_TAX = 3;
}

enum ReservationMovedTransactionType {
  RESERVATION_MOVED_TRANSACTION_TYPE_UNSPECIFIED = 0;
  RESERVATION_MOVED_TRANSACTION_TYPE_RATE = 1;
  RESERVATION_MOVED_TRANSACTION_TYPE_FEE = 2;
  RESERVATION_MOVED_TRANSACTION_TYPE_TAX = 3;
}

enum ReservationRoutedTransactionRelationType {
  RESERVATION_ROUTED_TRANSACTION_RELATION_TYPE_UNSPECIFIED = 0;
  RESERVATION_ROUTED_TRANSACTION_RELATION_TYPE_ROOM = 1;
  RESERVATION_ROUTED_TRANSACTION_RELATION_TYPE_RESERVATION = 2;
}

enum ReservationMovedTransactionRelationType {
  RESERVATION_MOVED_TRANSACTION_RELATION_TYPE_UNSPECIFIED = 0;
  RESERVATION_MOVED_TRANSACTION_RELATION_TYPE_ROOM = 1;
  RESERVATION_MOVED_TRANSACTION_RELATION_TYPE_RESERVATION = 2;
}

enum ReservationRoutedTransactionOperationType {
  RESERVATION_ROUTED_TRANSACTION_OPERATION_TYPE_UNSPECIFIED = 0;
  RESERVATION_ROUTED_TRANSACTION_OPERATION_TYPE_ROUTE = 1;
  RESERVATION_ROUTED_TRANSACTION_OPERATION_TYPE_UN_ROUTE = 2;
}

enum ReservationMovedTransactionOperationType {
  RESERVATION_MOVED_TRANSACTION_OPERATION_TYPE_UNSPECIFIED = 0;
  RESERVATION_MOVED_TRANSACTION_OPERATION_TYPE_MOVE = 1;
  RESERVATION_MOVED_TRANSACTION_OPERATION_TYPE_UNDO = 2;
}

enum ReservationMovedFolioSourceKind {
  RESERVATION_MOVED_FOLIO_SOURCE_KIND_UNSPECIFIED = 0;
  RESERVATION_MOVED_FOLIO_SOURCE_KIND_RESERVATION = 1;
  RESERVATION_MOVED_FOLIO_SOURCE_KIND_GROUP_PROFILE = 2;
}

// RESERVATION EVENTS
message ReservationCreatedEvent {
  Reservation reservation = 1;
  google.type.Date last_night_audit_date = 2;
}

message ReservationUpdatedEvent {
  Reservation reservation = 1;
  google.type.Date last_night_audit_date = 2;
}

message ReservationDeletedEvent {
  google.protobuf.Timestamp deleted_at = 1;
}

message ReservationStatusUpdatedEvent {
  ReservationStatus old_status = 1;
  ReservationStatus new_status = 2;
  google.type.Date last_night_audit_date = 3;
  string identifier = 4;
}

// RESERVATION ROOM EVENTS
message ReservationRoomCreatedEvent {
  ReservationRoom room = 1;
  google.type.Date last_night_audit_date = 2;
}

message ReservationRoomUpdatedEvent {
  ReservationRoom room = 1;
  google.type.Date last_night_audit_date = 2;
}

message ReservationRoomDeletedEvent {
  uint64 id = 1;
  google.protobuf.Timestamp deleted_at = 2;
}

message ReservationRoomRatesResetEvent {
  ReservationRoom room = 1;
  google.type.Date last_night_audit_date = 2;
}

// ADDON EVENTS
message ReservationAddonAddedEvent {
  Addon addon = 1;
}

message ReservationAddonUpdatedEvent {
  Addon addon = 1;
}

message ReservationAddonDeletedEvent {
  repeated uint64 addon_ids = 1;
  google.protobuf.Timestamp deleted_at = 2;
}

// ITEM EVENTS
message ReservationItemAddedEvent {
  ReservationItem item = 1;
}

// CUSTOM ITEM EVENTS
message ReservationCustomItemAddedEvent {
  ReservationCustomItem custom_item = 1;
}

message ReservationCustomItemDeletedEvent {
  uint64 id = 1;
  google.protobuf.Timestamp deleted_at = 2;
}

// ADJUSTMENT EVENTS
message ReservationAdjustmentAddedEvent {
  ReservationAdjustment adjustment = 1;
}

// EXCLUSIVE TAX EVENTS
message ReservationTaxAddedEvent {
  ReservationExclusiveTax tax = 1;
}

// EXCLUSIVE FEE EVENTS
message ReservationFeeAddedEvent {
  ReservationExclusiveFee fee = 1;
}

// PAYMENT EVENTS
message ReservationPaymentFlowCompletedEvent {
  ReservationStatus status = 1;
}

message ReservationTax {
  uint64 id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  uint64 parent_id = 4;
  ReservationTaxKind parent_kind = 5;
}

message ReservationFee {
  uint64 id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
}

message Reservation {
  ReservationStatus status = 1;
  uint64 guest_id = 2;
  google.protobuf.Timestamp transaction_datetime = 3; // This is the datetime when transactions for per reservation taxes / fees should be created
  google.type.Date checkin_date = 4;
  google.type.Date checkout_date = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  repeated ReservationTax taxes = 8;
  repeated ReservationFee fees = 9;
  string identifier = 10;
}

message Addon {
  uint64 id = 1;
  uint64 product_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  uint64 guest_id = 5;
  google.protobuf.Timestamp transaction_datetime = 6; // This is the datetime when transaction should be created
  uint64 booking_room_id = 7;
  bool posted = 8;
  repeated ReservationTax taxes = 9;
  repeated ReservationFee fees = 10;
  uint64 addon_id = 11;
}

message ReservationItem {
  uint64 id = 1;
  uint64 product_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  uint64 guest_id = 5;
  google.protobuf.Timestamp transaction_datetime = 6; // This is the datetime when transaction should be created
  optional uint64 folio_id = 7;
  bool posted = 8;
  uint32 quantity = 9;
  repeated ReservationTax taxes = 10;
  repeated ReservationFee fees = 11;
  uint64 booking_room_id = 12;
  string notes = 13;
}

message ReservationCustomItem {
  uint64 id = 1;
  uint64 product_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  uint64 guest_id = 5;
  google.protobuf.Timestamp transaction_datetime = 6; // This is the datetime when transaction should be created
  optional uint64 folio_id = 7;
  bool posted = 8;
  uint32 quantity = 9;
  repeated ReservationTax taxes = 10;
  repeated ReservationFee fees = 11;
  uint64 booking_room_id = 12;
  string notes = 13;
}

message ReservationAdjustment {
  string id = 1;
  uint64 reservation_room_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  uint64 guest_id = 5;
  google.protobuf.Timestamp transaction_datetime = 6; // This is the datetime when transaction should be created
  optional uint64 folio_id = 7;
  bool posted = 8;
  uint32 quantity = 9;
  oneof adjustment {
    ReservationAdjustmentItem item = 10;
    ReservationAdjustmentTax tax = 11;
    ReservationAdjustmentFee fee = 12;
    ReservationAdjustmentRoomRevenue room_revenue = 13;
    ReservationAdjustmentRate rate = 14;
  }
  string notes = 15;
}

message ReservationAdjustmentItem {
  uint64 product_id = 1;
  repeated ReservationTax taxes = 10;
  repeated ReservationFee fees = 11;
}

message ReservationAdjustmentTax {
  uint64 tax_id = 1;
}

message ReservationAdjustmentFee {
  uint64 fee_id = 1;
  repeated ReservationTax taxes = 2;
}

message ReservationAdjustmentRoomRevenue {
  ReservationRevenueEventType type = 1;
  uint64 room_type_id = 2;
  repeated ReservationTax taxes = 3;
  repeated ReservationFee fees = 4;
}

message ReservationAdjustmentRate {
  uint64 room_type_id = 1;
  repeated ReservationTax taxes = 2;
  repeated ReservationFee fees = 3;
}

message ReservationExclusiveTax {
  string id = 1;
  uint64 reservation_room_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  uint64 guest_id = 5;
  google.protobuf.Timestamp transaction_datetime = 6; // This is the datetime when transaction should be created
  optional uint64 folio_id = 7;
  bool posted = 8;
  uint32 quantity = 9;
  uint64 tax_id = 10;
  string notes = 11;
}

message ReservationExclusiveFee {
  string id = 1;
  uint64 reservation_room_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  uint64 guest_id = 5;
  google.protobuf.Timestamp transaction_datetime = 6; // This is the datetime when transaction should be created
  optional uint64 folio_id = 7;
  bool posted = 8;
  uint32 quantity = 9;
  uint64 fee_id = 10;
  repeated ReservationTax taxes = 11;
  string notes = 12;
}

message ReservationRoomNight {
  google.type.Date date = 1;
  cloudbeds.type.v1.Money amount = 2;
  repeated ReservationTax taxes = 3;
  repeated ReservationFee fees = 4;
}

message ReservationRoom {
  uint64 id = 1;
  uint64 guest_id = 2;
  google.type.TimeOfDay transaction_time = 3; // This is the time when transactions for reservation room should be created
  string room_id = 4; // Build by room_id-index where index is an int from 0 to n
  uint64 room_type_id = 5;
  string rate_name = 6;
  google.type.Date checkin_date = 7;
  google.type.Date checkout_date = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  repeated ReservationRoomNight nights = 11;
  ReservationRoomStatus status = 12;
  ReservationStatus reservation_status = 13; // Needed since taxes, fees can be added even for past transactions
}

// ROOM REVENUE
message ReservationRoomRevenue {
  string id = 1;
  bool posted = 2;
  cloudbeds.type.v1.Money amount = 3;
  ReservationRevenueEventType type = 4;
  google.protobuf.Timestamp transaction_datetime = 5;
  repeated ReservationTax taxes = 6;
  repeated ReservationFee fees = 7;
  uint64 reservation_room_id = 8;
  uint64 room_type_id = 9;
  optional uint64 folio_id = 10;
  uint64 guest_id = 11;
  string notes = 12;
}

message ReservationRoomRevenueAddedEvent {
  ReservationRoomRevenue room_revenue = 1;
}

// ROUTE TRANSACTIONS TO GROUPS
message ReservationRouteTransactionsEvent {
  repeated uint64 transaction_ids = 1;
  uint64 routed_to_source_id = 2;
}

// MOVE TRANSACTIONS TO FOLIO
message ReservationMoveTransactionsEvent {
  repeated uint64 transaction_ids = 1;
  uint64 folio_id = 2;
}

message ReservationRoutedPendingTaxDetails {
  uint64 id = 1; //tax_id
  uint64 parent_id = 2; //fee_id if tax on top of fee OR 0
}

message ReservationMovedPendingTaxDetails {
  uint64 id = 1; //tax_id
  uint64 parent_id = 2; //fee_id if tax on top of fee OR 0
}

message ReservationRoutedPendingFeeDetails {
  uint64 id = 1; //fee_id
}

message ReservationMovedPendingFeeDetails {
  uint64 id = 1; //fee_id
}

message ReservationRoutePendingTransactionParams {
  ReservationRoutedTransactionType type = 1; //rate/tax/fee
  ReservationRoutedTransactionRelationType relation_type = 2; // room | reservation
  google.type.Date service_date = 3; // 2023-11-22
  uint64 reservation_room_id = 4;
  oneof details {
    ReservationRoutedPendingTaxDetails tax_details = 5;
    ReservationRoutedPendingFeeDetails fee_details = 6;
  }
}

message ReservationMovePendingTransactionParams {
  ReservationMovedTransactionType type = 1; //rate/tax/fee
  ReservationMovedTransactionRelationType relation_type = 2; // room | reservation
  google.type.Date service_date = 3; // 2023-11-22
  uint64 reservation_room_id = 4;
  oneof details {
    ReservationMovedPendingTaxDetails tax_details = 5;
    ReservationMovedPendingFeeDetails fee_details = 6;
  }
  uint64 folio_id = 7;
  uint64 folio_source_id = 8;
  ReservationMovedFolioSourceKind folio_source_kind = 9;
}

// ROUTE TRANSACTIONS TO GROUPS
message ReservationRoutePendingTransactionsEvent {
  repeated ReservationRoutePendingTransactionParams transaction_params = 1;
  ReservationRoutedTransactionOperationType operation = 2;
  uint64 routed_to_source_id = 3;
}

// MOVE PENDING TRANSACTIONS TO GROUPS
message ReservationMovePendingTransactionsEvent {
  repeated ReservationMovePendingTransactionParams transaction_params = 1;
  ReservationMovedTransactionOperationType operation = 2;
}

message ReservationExternalRelation {
  string id = 1;
  ReservationExternalRelationType type = 2;
}

message ReservationTransactionVoidEvent {
  oneof identifier {
    uint64 transaction_id = 1;
    ReservationExternalRelation external_relation = 2;
  }
}

message ReservationAddonPostedEvent {
  repeated uint64 reservation_addon_ids = 1;
}

// RESERVATION EVENT
message ReservationEvent {
  uint64 id = 1;
  uint64 reservation_id = 2;
  ReservationEventType type = 3;
  uint64 property_id = 4;
  uint64 user_id = 5;
  string event_source = 6;
  google.protobuf.Timestamp event_timestamp = 7;
  oneof payload {
    ReservationCreatedEvent reservation_created = 8;
    ReservationUpdatedEvent reservation_updated = 9;
    ReservationDeletedEvent reservation_deleted = 10;
    ReservationStatusUpdatedEvent reservation_status_updated = 11;
    ReservationRoomCreatedEvent reservation_room_created = 12;
    ReservationRoomUpdatedEvent reservation_room_updated = 13;
    ReservationRoomDeletedEvent reservation_room_deleted = 14;
    ReservationRoomRevenueAddedEvent reservation_room_revenue_added = 15;
    ReservationRouteTransactionsEvent reservation_route_transactions = 16;
    ReservationAddonAddedEvent addon_added_event = 17;
    ReservationItemAddedEvent item_added_event = 18;
    ReservationAdjustmentAddedEvent adjustment_added_event = 19;
    ReservationTaxAddedEvent tax_added_event = 20;
    ReservationFeeAddedEvent fee_added_event = 21;
    ReservationTransactionVoidEvent void_event = 22;
    ReservationCustomItemAddedEvent custom_item_added_event = 23;
    ReservationAddonPostedEvent addon_posted_event = 24;
    ReservationRoutePendingTransactionsEvent reservation_route_pending_transactions = 25;
    ReservationMovePendingTransactionsEvent reservation_move_pending_transactions = 26;
    ReservationMoveTransactionsEvent reservation_move_transactions = 27;
    ReservationRoomRatesResetEvent reservation_room_rates_reset_event = 28;
    ReservationAddonUpdatedEvent addon_updated_event = 100;
    ReservationAddonDeletedEvent addon_deleted_event = 101;
    ReservationCustomItemDeletedEvent custom_item_deleted_event = 102;
    ReservationPaymentFlowCompletedEvent payment_flow_completed_event = 200;
  }
}
