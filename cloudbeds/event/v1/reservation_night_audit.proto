syntax = "proto3";

package cloudbeds.event.v1;

option go_package = "github.com/cloudbeds/protos-go/event/v1;cloudbeds";
option java_package = "com.cloudbeds.event.v1";
option java_outer_classname = "ReservationNightAuditEventProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Event\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Event\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";

message NightAuditedReservation {
  uint64 reservation_id = 1;
  repeated uint64 reservation_room_id = 2;
}

// RESERVATION NIGHT AUDIT EVENT
message ReservationNightAuditEvent {
  uint64 id = 1;
  uint64 property_id = 2;
  google.protobuf.Timestamp started_at = 3;
  google.protobuf.Timestamp process_from = 4;
  google.protobuf.Timestamp process_to = 5;
  repeated NightAuditedReservation reservations = 6;
}

message ReservationNightAuditEventKey {
  uint64 property_id = 1;
}