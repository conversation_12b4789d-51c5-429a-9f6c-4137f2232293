syntax = "proto3";

package cloudbeds.event.v1;

option go_package = "github.com/cloudbeds/protos-go/event/v1;cloudbeds";
option java_package = "com.cloudbeds.event.v1";
option java_outer_classname = "ReviewEventProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Event\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Event\\V1\\GPBMetadata";

// ENUMS
enum ReviewStatus {
  REVIEW_STATUS_UNSPECIFIED = 0;
  REVIEW_STATUS_PENDING = 1;
  REVIEW_STATUS_APPROVED = 2;
  REVIEW_STATUS_REJECTED = 3;
}

message ReviewEvent {
  uint64 id = 1 [deprecated = true];
  Property property = 2;
  string channel_id = 3;
  string myallocator_id = 4;
  string order_id = 5;
  string brand_name = 6;
  repeated Guest guests = 7;
  GuestReview guest_review = 8;
  string cb_id = 9;
  HostReview host_review = 10;
}

// {} property
message Property {
  string ota_id = 1;
  string mfd_id = 2;
  string ma_id = 3;
}

// [{}] revivew.guests
message Guest {
  string first_name = 1;
  string last_name = 2;
  string url = 3;
  bool primary = 4;
}

// {} guest_review
message GuestReview {
  string id = 1;
  Ratings ratings = 2;
  Review review = 3;
  Response response = 4;
  Feedback feedback = 5;
  bool hidden = 6;
  bool submitted = 7;
}

message HostReview {
  string id = 1;
  bool hidden = 2;
  bool submitted = 3;
  Review review = 4;
  Response response = 5;
  Feedback feedback = 6;
  Ratings ratings = 7;
}

message Feedback {
  FeedbackBody body = 1;
  bool private = 2;

  // only applicable for host reviews
  bool is_guest_recommended = 3;
}

// {} guest_review.ratings
message Ratings {
  repeated RatingByCategory by_category = 1;
  string overall = 2;
}

// [{}] guest_review.ratings.by_category
message RatingByCategory {
  string code = 1;
  string value = 2;
  string comment = 3;
  repeated string tags = 4;
}

// {} guest_review.review
message Review {
  ReviewBody body = 1;
  ReviewTitle title = 2;
  string submitted_at = 3;
  string expires_at = 4;
}

// {} guest_review.review.body
message ReviewBody {
  string locale = 1;
  string value = 2;
}

// {} guest_review.review.title
message ReviewTitle {
  string locale = 1;
  string value = 2;
}

// {} guest_review.response.body
message ResponseBody {
  string locale = 1;
  string value = 2;
}

message FeedbackBody {
  string locale = 1;
  string value = 2;
}

// {} guest_review.response
message Response {
  bool eligible = 1;
  ResponseBody body = 2;
  ReviewStatus status = 3;
  string submitted_at = 4;
}

