syntax = "proto3";

package cloudbeds.event.v1;

option go_package = "github.com/cloudbeds/protos-go/event/v1;cloudbeds";
option java_package = "com.cloudbeds.event.v1";
option java_outer_classname = "AllotmentBlockEventTypeProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Event\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Event\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

// ENUMS
enum AllotmentBlockEventType {
  ALLOTMENT_BLOCK_EVENT_TYPE_UNSPECIFIED = 0;
  ALLOTMENT_BLOCK_EVENT_TYPE_CREATED = 1;
  ALLOTMENT_BLOCK_EVENT_TYPE_UPDATED = 2;
  ALLOTMENT_BLOCK_EVENT_TYPE_DELETED = 3;
}

// ALLOTMENT BLOCK EVENT
message AllotmentBlockEvent {
  uint64 id = 1;
  uint64 allotment_block_id = 2;
  uint64 group_profile_id = 3;
  AllotmentBlockEventType type = 4;
  uint64 property_id = 5;
  uint64 user_id = 6;
  string event_source = 7;
  google.protobuf.Timestamp event_timestamp = 8;
  google.type.Date interval_start_date = 9;
  google.type.Date interval_end_date = 10;
  optional string allotment_block_code = 11;
  optional string group_code = 12;
}

enum AllotmentBlocksEventType {
  ALLOTMENT_BLOCKS_EVENT_TYPE_UNSPECIFIED = 0;
  ALLOTMENT_BLOCKS_EVENT_TYPE_RATE_UPDATED = 1;
  ALLOTMENT_BLOCKS_EVENT_TYPE_ROOM_AVAILABILITY_UPDATED = 2;
}

message AllotmentBlockObject {
  uint64 allotment_block_id = 1;
  string allotment_block_code = 2;
  uint64 group_profile_id = 3;
  string group_code = 4;
}

message AllotmentBlocksEvent {
  uint64 id = 1;
  repeated AllotmentBlockObject allotment_block = 2;
  AllotmentBlocksEventType type = 3;
  uint64 property_id = 4;
  uint64 user_id = 5;
  string event_source = 6;
  google.protobuf.Timestamp event_timestamp = 7;
}
