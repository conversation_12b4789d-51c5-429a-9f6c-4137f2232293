syntax = "proto3";

package cloudbeds.event.v1;

option go_package = "github.com/cloudbeds/protos-go/event/v1;cloudbeds";
option java_package = "com.cloudbeds.event.v1";
option java_outer_classname = "ChannelPropertyScoreEventProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Event\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Event\\V1\\GPBMetadata";

import "cloudbeds/event/v1/review.proto";

message ChannelPropertyScoreEvent {
  string cb_id = 1;
  Property property = 2;
  string channel_id = 3;
  repeated Brand brands = 4;
}

message ChannelPropertyScoreRatings {
  repeated ChannelPropertyScoreRatingByCategory by_category = 1;
  string overall = 2;
  string total_reviews = 3;
}

message ChannelPropertyScoreRatingByCategory {
  string code = 1;
  string value = 2;
  string reviews = 3;
  map<string, string> tag_map = 4;
}

message Brand {
  string name = 1;
  ChannelPropertyScoreRatings ratings = 2;
}
