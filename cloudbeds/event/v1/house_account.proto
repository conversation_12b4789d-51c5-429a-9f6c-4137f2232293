syntax = "proto3";

package cloudbeds.event.v1;

option go_package = "github.com/cloudbeds/protos-go/event/v1;cloudbeds";
option java_package = "com.cloudbeds.event.v1";
option java_outer_classname = "HouseAccountEventProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Event\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Event\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";
import "cloudbeds/type/v1/money.proto";

// ENUMS
enum HouseAccountEventType {
  HOUSE_ACCOUNT_EVENT_TYPE_UNSPECIFIED = 0;
  HOUSE_ACCOUNT_EVENT_TYPE_ROOM_REVENUE_ADDED = 1;
  HOUSE_ACCOUNT_EVENT_TYPE_ITEM_ADDED = 2;
  HOUSE_ACCOUNT_EVENT_TYPE_ADJUSTMENT_ADDED = 3;
  HOUSE_ACCOUNT_EVENT_TYPE_TAX_ADDED = 4;
  HOUSE_ACCOUNT_EVENT_TYPE_FEE_ADDED = 5;
  HOUSE_ACCOUNT_EVENT_TYPE_CUSTOM_ITEM_ADDED = 6;
  HOUSE_ACCOUNT_EVENT_TYPE_CUSTOM_ITEM_DELETED = 8;
  HOUSE_ACCOUNT_EVENT_TYPE_VOID = 9;
}

enum HouseAccountTaxKind {
  HOUSE_ACCOUNT_TAX_KIND_UNSPECIFIED = 0;
  HOUSE_ACCOUNT_TAX_KIND_FEE = 1;
}

enum HouseAccountRevenueEventType {
  HOUSE_ACCOUNT_REVENUE_EVENT_TYPE_UNSPECIFIED = 0;
  HOUSE_ACCOUNT_REVENUE_EVENT_TYPE_MANUAL = 1;
  HOUSE_ACCOUNT_REVENUE_EVENT_TYPE_NO_SHOW = 2;
  HOUSE_ACCOUNT_REVENUE_EVENT_TYPE_CANCELLATION = 3;
}

enum HouseAccountExternalRelationType {
  HOUSE_ACCOUNT_EXTERNAL_RELATION_TYPE_UNSPECIFIED = 0;
  HOUSE_ACCOUNT_EXTERNAL_RELATION_TYPE_ITEM = 1;
  HOUSE_ACCOUNT_EXTERNAL_RELATION_TYPE_ADDON = 2;
  HOUSE_ACCOUNT_EXTERNAL_RELATION_TYPE_ADJUSTMENT = 3;
  HOUSE_ACCOUNT_EXTERNAL_RELATION_TYPE_EXCLUSIVE_TAX = 4;
  HOUSE_ACCOUNT_EXTERNAL_RELATION_TYPE_EXCLUSIVE_FEE = 5;
  HOUSE_ACCOUNT_EXTERNAL_RELATION_TYPE_ROOM_REVENUE = 6;
  HOUSE_ACCOUNT_EXTERNAL_RELATION_TYPE_ITEM_POS = 7;
}

// TAX
message HouseAccountTax {
  uint64 id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  uint64 parent_id = 4;
  HouseAccountTaxKind parent_kind = 5;
}

// FEE
message HouseAccountFee {
  uint64 id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
}

// ROOM REVENUE EVENT
message HouseAccountRoomRevenue {
  string id = 1;
  bool posted = 2;
  cloudbeds.type.v1.Money amount = 3;
  HouseAccountRevenueEventType type = 4;
  google.protobuf.Timestamp transaction_datetime = 5;
  repeated HouseAccountTax taxes = 6;
  repeated HouseAccountFee fees = 7;
  string notes = 8;
}

message HouseAccountRoomRevenueAddedEvent {
  HouseAccountRoomRevenue room_revenue = 1;
}

// ITEM EVENTS
message HouseAccountItemAddedEvent {
  HouseAccountItem item = 1;
}

// CUSTOM ITEM EVENTS
message HouseAccountCustomItemAddedEvent {
  HouseAccountCustomItem custom_item = 1;
}

message HouseAccountCustomItemDeletedEvent {
  uint64 id = 1;
  google.protobuf.Timestamp deleted_at = 2;
}

// ADJUSTMENT EVENTS
message HouseAccountAdjustmentAddedEvent {
  HouseAccountAdjustment adjustment = 1;
}

// EXCLUSIVE TAX EVENTS
message HouseAccountTaxAddedEvent {
  HouseAccountExclusiveTax tax = 1;
}

// EXCLUSIVE FEE EVENTS
message HouseAccountFeeAddedEvent {
  HouseAccountExclusiveFee fee = 1;
}

message HouseAccountItem {
  uint64 id = 1;
  uint64 product_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  google.protobuf.Timestamp transaction_datetime = 5; // This is the datetime when transaction should be created
  optional uint64 folio_id = 6;
  bool posted = 7;
  uint32 quantity = 8;
  repeated HouseAccountTax taxes = 9;
  repeated HouseAccountFee fees = 10;
  string notes = 11;
}

message HouseAccountCustomItem {
  uint64 id = 1;
  uint64 product_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  google.protobuf.Timestamp transaction_datetime = 5; // This is the datetime when transaction should be created
  optional uint64 folio_id = 6;
  bool posted = 7;
  uint32 quantity = 8;
  repeated HouseAccountTax taxes = 9;
  repeated HouseAccountFee fees = 10;
  string notes = 11;
}

message HouseAccountAdjustment {
  string id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  google.protobuf.Timestamp transaction_datetime = 4; // This is the datetime when transaction should be created
  optional uint64 folio_id = 5;
  bool posted = 6;
  uint32 quantity = 7;
  oneof adjustment {
    HouseAccountAdjustmentItem item = 8;
    HouseAccountAdjustmentTax tax = 9;
    HouseAccountAdjustmentFee fee = 10;
    HouseAccountAdjustmentRoomRevenue room_revenue = 11;
    HouseAccountAdjustmentRate rate = 13;
  }
  string notes = 12;
}

message HouseAccountAdjustmentRate {
  repeated HouseAccountTax taxes = 1;
  repeated HouseAccountFee fees = 2;
}

message HouseAccountAdjustmentItem {
  uint64 product_id = 1;
  repeated HouseAccountTax taxes = 10;
  repeated HouseAccountFee fees = 11;
}

message HouseAccountAdjustmentTax {
  uint64 tax_id = 1;
}

message HouseAccountAdjustmentFee {
  uint64 fee_id = 1;
  repeated HouseAccountTax taxes = 2;
}

message HouseAccountAdjustmentRoomRevenue {
  HouseAccountRevenueEventType type = 1;
  repeated HouseAccountTax taxes = 2;
  repeated HouseAccountFee fees = 3;
}

message HouseAccountExclusiveTax {
  string id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  uint64 guest_id = 4;
  uint64 tax_id = 5;
  google.protobuf.Timestamp transaction_datetime = 6; // This is the datetime when transaction should be created
  optional uint64 folio_id = 7;
  bool posted = 8;
  uint32 quantity = 9;
  string notes = 10;
}

message HouseAccountExclusiveFee {
  string id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  uint64 guest_id = 4;
  uint64 fee_id = 5;
  google.protobuf.Timestamp transaction_datetime = 6; // This is the datetime when transaction should be created
  optional uint64 folio_id = 7;
  bool posted = 8;
  uint32 quantity = 9;
  repeated HouseAccountTax taxes = 10;
  string notes = 11;
}

message HouseAccountExternalRelation {
  string id = 1;
  HouseAccountExternalRelationType type = 2;
}

message HouseAccountTransactionVoidEvent {
  oneof identifier {
    uint64 transaction_id = 1;
    HouseAccountExternalRelation external_relation = 2;
  }
}

// HOUSE ACCOUNT EVENT
message HouseAccountEvent {
  uint64 id = 1;
  uint64 house_account_id = 2;
  HouseAccountEventType type = 3;
  uint64 property_id = 4;
  uint64 user_id = 5;
  string event_source = 6;
  google.protobuf.Timestamp event_timestamp = 7;
  oneof payload {
    HouseAccountRoomRevenueAddedEvent house_account_room_revenue_added = 8;
    HouseAccountItemAddedEvent item_added_event = 9;
    HouseAccountCustomItemAddedEvent custom_item_added_event = 10;
    HouseAccountAdjustmentAddedEvent adjustment_added_event = 11;
    HouseAccountTaxAddedEvent tax_added_event = 12;
    HouseAccountFeeAddedEvent fee_added_event = 13;
    HouseAccountTransactionVoidEvent void_event = 14;
    HouseAccountCustomItemDeletedEvent custom_item_deleted_event = 101;
  }
}