syntax = "proto3";

package cloudbeds.event.v1;

option go_package = "github.com/cloudbeds/protos-go/event/v1;cloudbeds";
option java_package = "com.cloudbeds.event.v1";
option java_outer_classname = "GroupProfileEventProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Event\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Event\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";
import "cloudbeds/type/v1/money.proto";

// ENUMS
enum GroupProfileEventType {
  GROUP_PROFILE_EVENT_TYPE_UNSPECIFIED = 0;
  GROUP_PROFILE_EVENT_TYPE_ROOM_REVENUE_ADDED = 1;
  GROUP_PROFILE_EVENT_TYPE_ROUTE_TRANSACTIONS = 2;
  GROUP_PROFILE_EVENT_TYPE_ITEM_ADDED = 3;
  GROUP_PROFILE_EVENT_TYPE_ADJUSTMENT_ADDED = 4;
  GROUP_PROFILE_EVENT_TYPE_TAX_ADDED = 5;
  GROUP_PROFILE_EVENT_TYPE_FEE_ADDED = 6;
  GROUP_PROFILE_EVENT_TYPE_VOID = 7;
  GROUP_PROFILE_EVENT_TYPE_CREATED = 8;
  GROUP_PROFILE_EVENT_TYPE_UPDATED = 9;
  GROUP_PROFILE_EVENT_TYPE_DELETED = 10;
  GROUP_PROFILE_EVENT_TYPE_CUSTOM_ITEM_ADDED = 11;
  GROUP_PROFILE_EVENT_TYPE_CUSTOM_ITEM_DELETED = 12;
  GROUP_PROFILE_EVENT_TYPE_MOVE_TRANSACTIONS = 13;
}

enum GroupProfileTaxKind {
  GROUP_PROFILE_TAX_KIND_UNSPECIFIED = 0;
  GROUP_PROFILE_TAX_KIND_FEE = 1;
}

enum GroupProfileRevenueEventType {
  GROUP_PROFILE_REVENUE_EVENT_TYPE_UNSPECIFIED = 0;
  GROUP_PROFILE_REVENUE_EVENT_TYPE_MANUAL = 1;
  GROUP_PROFILE_REVENUE_EVENT_TYPE_NO_SHOW = 2;
  GROUP_PROFILE_REVENUE_EVENT_TYPE_CANCELLATION = 3;
}

enum GroupProfileExternalRelationType {
  GROUP_PROFILE_EXTERNAL_RELATION_TYPE_UNSPECIFIED = 0;
  GROUP_PROFILE_EXTERNAL_RELATION_TYPE_ITEM = 1;
  GROUP_PROFILE_EXTERNAL_RELATION_TYPE_ADDON = 2;
  GROUP_PROFILE_EXTERNAL_RELATION_TYPE_ADJUSTMENT = 3;
  GROUP_PROFILE_EXTERNAL_RELATION_TYPE_EXCLUSIVE_TAX = 4;
  GROUP_PROFILE_EXTERNAL_RELATION_TYPE_EXCLUSIVE_FEE = 5;
  GROUP_PROFILE_EXTERNAL_RELATION_TYPE_ROOM_REVENUE = 6;
  GROUP_PROFILE_EXTERNAL_RELATION_TYPE_ITEM_POS = 7;
}

// TAX
message GroupProfileTax {
  uint64 id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  uint64 parent_id = 4;
  GroupProfileTaxKind parent_kind = 5;
}

// FEE
message GroupProfileFee {
  uint64 id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
}

// ROOM REVENUE EVENT
message GroupProfileRoomRevenue {
  string id = 1;
  bool posted = 2;
  cloudbeds.type.v1.Money amount = 3;
  GroupProfileRevenueEventType type = 4;
  google.protobuf.Timestamp transaction_datetime = 5;
  repeated GroupProfileTax taxes = 6;
  repeated GroupProfileFee fees = 7;
  optional uint64 folio_id = 8;
  string notes = 9;
}

message GroupProfileRoomRevenueAddedEvent {
  GroupProfileRoomRevenue room_revenue = 1;
}

// ROUTE TRANSACTIONS TO RESERVATIONS
message GroupProfileRouteTransactionsEvent {
  repeated uint64 transaction_ids = 1;
  uint64 routed_to_source_id = 2;
}

// MOVE TRANSACTIONS TO FOLIO
message GroupProfileMoveTransactionsEvent {
  repeated uint64 transaction_ids = 1;
  uint64 folio_id = 2;
}

// ITEM EVENTS
message GroupProfileItemAddedEvent {
  GroupProfileItem item = 1;
}

// CUSTOM ITEM EVENTS
message GroupProfileCustomItemAddedEvent {
  GroupProfileCustomItem custom_item = 1;
}

message GroupProfileCustomItemDeletedEvent {
  uint64 id = 1;
  google.protobuf.Timestamp deleted_at = 2;
}

// ADJUSTMENT EVENTS
message GroupProfileAdjustmentAddedEvent {
  GroupProfileAdjustment adjustment = 1;
}

// EXCLUSIVE TAX EVENTS
message GroupProfileTaxAddedEvent {
  GroupProfileExclusiveTax tax = 1;
}

// EXCLUSIVE FEE EVENTS
message GroupProfileFeeAddedEvent {
  GroupProfileExclusiveFee fee = 1;
}

message GroupProfileItem {
  uint64 id = 1;
  uint64 product_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  google.protobuf.Timestamp transaction_datetime = 5; // This is the datetime when transaction should be created
  optional uint64 folio_id = 6;
  bool posted = 7;
  uint32 quantity = 8;
  repeated GroupProfileTax taxes = 9;
  repeated GroupProfileFee fees = 10;
  string notes = 11;
}

message GroupProfileCustomItem {
  uint64 id = 1;
  uint64 product_id = 2;
  string description = 3;
  cloudbeds.type.v1.Money amount = 4;
  google.protobuf.Timestamp transaction_datetime = 5; // This is the datetime when transaction should be created
  optional uint64 folio_id = 6;
  bool posted = 7;
  uint32 quantity = 8;
  repeated GroupProfileTax taxes = 9;
  repeated GroupProfileFee fees = 10;
  string notes = 11;
}

message GroupProfileAdjustment {
  string id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  google.protobuf.Timestamp transaction_datetime = 4; // This is the datetime when transaction should be created
  optional uint64 folio_id = 5;
  bool posted = 6;
  uint32 quantity = 7;
  oneof adjustment {
    GroupProfileAdjustmentItem item = 8;
    GroupProfileAdjustmentTax tax = 9;
    GroupProfileAdjustmentFee fee = 10;
    GroupProfileAdjustmentRoomRevenue room_revenue = 11;
    GroupProfileAdjustmentRate rate = 13;
  }
  string notes = 12;
}

message GroupProfileAdjustmentItem {
  uint64 product_id = 1;
  repeated GroupProfileTax taxes = 10;
  repeated GroupProfileFee fees = 11;
}

message GroupProfileAdjustmentTax {
  uint64 tax_id = 1;
}

message GroupProfileAdjustmentFee {
  uint64 fee_id = 1;
  repeated GroupProfileTax taxes = 2;
}

message GroupProfileAdjustmentRoomRevenue {
  GroupProfileRevenueEventType type = 1;
  repeated GroupProfileTax taxes = 2;
  repeated GroupProfileFee fees = 3;
}

message GroupProfileAdjustmentRate {
  repeated GroupProfileTax taxes = 1;
  repeated GroupProfileFee fees = 2;
}

message GroupProfileExclusiveTax {
  string id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  uint64 tax_id = 4;
  google.protobuf.Timestamp transaction_datetime = 5; // This is the datetime when transaction should be created
  optional uint64 folio_id = 6;
  bool posted = 7;
  uint32 quantity = 8;
  string notes = 9;
}

message GroupProfileExclusiveFee {
  string id = 1;
  string description = 2;
  cloudbeds.type.v1.Money amount = 3;
  uint64 fee_id = 4;
  google.protobuf.Timestamp transaction_datetime = 5; // This is the datetime when transaction should be created
  optional uint64 folio_id = 6;
  bool posted = 7;
  uint32 quantity = 8;
  repeated GroupProfileTax taxes = 9;
  string notes = 10;
}

message GroupProfileExternalRelation {
  string id = 1;
  GroupProfileExternalRelationType type = 2;
}

message GroupProfileTransactionVoidEvent {
  oneof identifier {
    uint64 transaction_id = 1;
    GroupProfileExternalRelation external_relation = 2;
  }
}

// GROUP PROFILE EVENT
message GroupProfileEvent {
  uint64 id = 1;
  uint64 group_profile_id = 2;
  GroupProfileEventType type = 3;
  uint64 property_id = 4;
  uint64 user_id = 5;
  string event_source = 6;
  google.protobuf.Timestamp event_timestamp = 7;
  oneof payload {
    GroupProfileRoomRevenueAddedEvent group_profile_room_revenue_added = 8;
    GroupProfileRouteTransactionsEvent group_profile_route_transactions = 9;
    GroupProfileItemAddedEvent item_added_event = 10;
    GroupProfileAdjustmentAddedEvent adjustment_added_event = 11;
    GroupProfileTaxAddedEvent tax_added_event = 12;
    GroupProfileFeeAddedEvent fee_added_event = 13;
    GroupProfileTransactionVoidEvent void_event = 14;
    GroupProfileCustomItemAddedEvent custom_item_added_event = 16;
    GroupProfileCustomItemDeletedEvent custom_item_deleted_event = 17;
    GroupProfileMoveTransactionsEvent group_profile_move_transactions = 18;
  }
  optional string group_code = 15;
}