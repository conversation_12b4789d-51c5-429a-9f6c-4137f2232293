syntax = "proto3";

package cloudbeds.event.v1;

option go_package = "github.com/cloudbeds/protos-go/event/v1;cloudbeds";
option java_package = "com.cloudbeds.event.v1";
option java_outer_classname = "AllotmentBlockEventKeyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Event\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Event\\V1\\GPBMetadata";

message AllotmentBlockEventKey {
  uint64 id = 1;
}

message AllotmentBlocksEventKey {
  uint64 id = 1;
}
