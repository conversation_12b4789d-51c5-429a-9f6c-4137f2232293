syntax = "proto3";

import "google/protobuf/timestamp.proto";

package cloudbeds.guestportal.v1;

option go_package = "github.com/cloudbeds/protos-go/guestportal/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "GuestPortalProto";
option java_package = "com.cloudbeds.guestportal.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\GuestPortal\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\GuestPortal\\V1";

service GuestPortalService {
  rpc CreateGuestMagicLink(CreateGuestMagicLinkRequest) returns (CreateGuestMagicLinkResponse);
  rpc GetGuestJWT(GetGuestJWTRequest) returns (GetGuestJWTResponse);
}

message CreateGuestMagicLinkRequest {
  int64 property_id = 1;
  int64 guest_id = 2;
  optional google.protobuf.Timestamp expires_at = 3;
  optional string guest_app = 4;
}

message CreateGuestMagicLinkResponse {
  string url = 1;
  string identity_token = 2;
}

message ExchangeIdentityToken {
  string identity_token = 1;
}

message CreateGuestJWT {
  int64 property_id = 1;
  int64 guest_id = 2;
}

message RefreshGuestJWT {
  string refresh_token = 1;
}

message GetGuestJWTRequest {
  oneof data {
    CreateGuestJWT create = 1;
    RefreshGuestJWT refresh = 2;
    ExchangeIdentityToken exchange = 3;
  }
}

message GetGuestJWTResponse {
  int64 property_id = 1;
  int64 guest_id = 2; 
  string access_token = 3;
  string refresh_token = 4;
}
