syntax = "proto3";

package cloudbeds.image.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/image/v1;image";
option java_multiple_files = true;
option java_package = "com.cloudbeds.image.v1";
option php_namespace = "Cloudbeds\\Protos\\Image\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Image\\V1\\GPBMetadata";

service ImageService {
  rpc UploadImage (stream UploadImageRequest) returns (UploadImageResponse);
  rpc DownloadImage (DownloadImageRequest) returns (stream DownloadImageResponse);
  rpc SignUrl (SignUrlRequest) returns (SignUrlResponse);
  rpc PutSignUrl (PutSignUrlRequest) returns (PutSignUrlResponse);
}

message UploadImageRequest {
  File file = 1;
  map<string, string> tags = 2;
  Origin origin = 3;
}

message UploadImageResponse {
  string id = 1;
}

message DownloadImageRequest {
  string id = 1;
}

message DownloadImageResponse {
  File file = 1;
  map<string, string> tags = 2;
}

message SignUrlRequest {
  string id = 1;
  google.protobuf.Timestamp valid_until = 2;
}

message SignUrlResponse {
  string signed_url = 1;
}

message PutSignUrlRequest {
  Origin origin = 1;
  google.protobuf.Timestamp valid_until = 2;
}

message PutSignUrlResponse {
  string signed_url = 1;
}

enum Origin {
  ORIGIN_UNSPECIFIED = 0;
  ORIGIN_MESSAGING = 1;
}

message File {
  int64 size = 1;
  bytes image_bytes = 2;
  string content_type = 3;
}
