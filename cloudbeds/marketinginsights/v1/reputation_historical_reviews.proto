syntax = "proto3";

package cloudbeds.marketinginsights.v1;

option go_package = "github.com/cloudbeds/protos-go/marketinginsights/v1;marketinginsights";
option java_package = "com.cloudbeds.marketinginsights.v1";
option java_outer_classname = "ReputationHistoricalReviewsProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1\\GPBMetadata";

message ReputationHistoricalReviews {
  string id = 1;
  string property_id = 2;
  string channel_id = 3;
}
