syntax = "proto3";

package cloudbeds.marketinginsights.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/marketinginsights/v1;marketinginsights";
option java_package = "com.cloudbeds.marketinginsights.v1";
option java_outer_classname = "ReputationSentimentAnalysisResultsProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1\\GPBMetadata";

message ReputationSentimentAnalysisResults {
  string property_id = 1;

  message Summary {
    map<string, string> positive = 1;
    map<string, string> negative = 2;
    map<string, string> neutral = 3;
  }

  Summary summary = 2;

  message Tag {
    map<string, string> label = 1;

    Summary summary = 2;

    repeated string review_ids = 3;
    string rating = 4;
  }

  map<string, Tag> tags = 3;

  google.protobuf.Timestamp created_at = 4;
}