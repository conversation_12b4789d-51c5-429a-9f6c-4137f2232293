syntax = "proto3";

package cloudbeds.marketinginsights.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/marketinginsights/v1;marketinginsights";
option java_package = "com.cloudbeds.marketinginsights.v1";
option java_outer_classname = "RetargetingReportMetricProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1\\GPBMetadata";

message RetargetingReportMetric {
  string id = 1;
  string property_id = 2;
  string advertisable_id = 3;
  string campaign_id = 4;
  google.protobuf.Timestamp date = 5;
  int64 impressions = 6;
  int64 clicks = 7;
  double cost = 8;
  google.protobuf.Timestamp created_at = 9;
  optional google.protobuf.Timestamp updated_at = 10;
  double cost_markup = 11;
}
