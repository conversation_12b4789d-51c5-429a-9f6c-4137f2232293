syntax = "proto3";

package cloudbeds.marketinginsights.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/marketinginsights/v1;marketinginsights";
option java_package = "com.cloudbeds.marketinginsights.v1";
option java_outer_classname = "RetargetingCampaignProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1\\GPBMetadata";

message RetargetingCampaign {
  string id = 1;
  string property_id = 2;
  string advertisable_id = 3;
  optional string user = 4;
  optional string property_currency = 5;
  string status = 6;
  string visibility = 7;
  google.protobuf.Timestamp created_at = 8;
  optional google.protobuf.Timestamp updated_at = 9;
}
