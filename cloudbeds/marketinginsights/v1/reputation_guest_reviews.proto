syntax = "proto3";

package cloudbeds.marketinginsights.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/marketinginsights/v1;marketinginsights";
option java_package = "com.cloudbeds.marketinginsights.v1";
option java_outer_classname = "ReputationGuestReviewsProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1\\GPBMetadata";

message ReputationGuestReviews {
  string id = 1;
  string property_id = 2;
  string subscription_tier = 3;
  string channel_id = 4;
  optional string channel_reservation_id = 5;
  string review = 6;
  int64 rating = 7;
  repeated Category categories = 8;
  google.protobuf.Timestamp created_at = 9;
  optional google.protobuf.Timestamp updated_at = 10;
  optional bool deleted = 11;
}

message Category {
  string code = 1;
  int64 rating = 2;
  repeated string tags = 3;
  optional string comment = 4;
}