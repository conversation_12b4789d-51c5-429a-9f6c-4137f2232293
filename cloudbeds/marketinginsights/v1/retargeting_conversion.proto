syntax = "proto3";

package cloudbeds.marketinginsights.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/marketinginsights/v1;marketinginsights";
option java_package = "com.cloudbeds.marketinginsights.v1";
option java_outer_classname = "RetargetingConversionProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\MarketingInsights\\V1\\GPBMetadata";

message RetargetingConversion {
  string id = 1;
  string adroll_conversion_id = 2;
  string property_id = 3;
  string advertisable_id = 4;
  string campaign_id = 5;
  string ad_group_id = 6;
  string ad_id = 7;
  string ad_size = 8;
  google.protobuf.Timestamp conversion_date = 9;
  string reservation_identifier = 10;
  int64 conversion_value_usd = 11;
  int64 conversion_value = 12;
  string conversion_currency = 13;
  google.protobuf.Timestamp created_at = 14;
  optional google.protobuf.Timestamp updated_at = 15;
}
