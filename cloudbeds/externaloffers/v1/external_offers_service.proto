syntax = "proto3";

import "cloudbeds/externaloffers/v1/external_offers.proto";
import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";

package cloudbeds.externaloffers.v1;

option go_package = "github.com/cloudbeds/protos-go/externaloffers/v1;externaloffers";
option java_package = "com.cloudbeds.externaloffers.v1";
option java_outer_classname = "ExternalOffersApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\ExternalOffers\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\ExternalOffers\\V1\\GPBMetadata";

service ExternalOffersService {
    rpc ReadOfferByProviderId(ReadOfferByProviderIdRequest) returns (ReadOfferByProviderIdResponse);
    rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);
    rpc PrepareOrders(PrepareOrdersRequest) returns (PrepareOrdersResponse);
    rpc ConfirmOrder(ConfirmOrderRequest) returns (ConfirmOrderResponse);
    rpc VoidOrder(VoidOrderRequest) returns (VoidOrderResponse);
    rpc CreateSessionEvent(CreateSessionEventRequest) returns (CreateSessionEventResponse);
    rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);
    rpc GetProviderOrderUrl(GetProviderOrderUrlRequest) returns (GetProviderOrderUrlResponse);
    rpc FulfillOrder(FulfillOrderRequest) returns (FulfillOrderResponse);
  }
  
message ReadOfferByProviderIdRequest {
  Provider provider = 1;
  repeated string ids = 2;
  string locale = 3;
}

message ReadOfferByProviderIdResponse {
  repeated Offer offers = 1;
}

message CreateSessionEventRequest {
  string session_id = 1;
  SessionEventType type = 2; // "booking_confirmed"
}

message CreateSessionEventResponse {}

message CreateOrderRequest {
  int64 property_id = 1;
  Provider provider = 2;
  int64 reservation_id = 3;
  int64 guest_id = 4;
  string guest_email = 5;
  string guest_locale = 6;
  string currency = 7;
  string payment_method_id = 8;
  repeated string offer_ids = 9;
  repeated Accommodation accommodations = 10;
}

message CreateOrderResponse {
  Order order = 1;
}

message PrepareOrdersRequest {
  int64 property_id = 1;
  int64 reservation_id = 2;
}

message PrepareOrdersResponse {
  repeated Order orders = 1;
}

message ConfirmOrderRequest {
  int64 id = 1;
  int64 reservation_id = 2;
}

message ConfirmOrderResponse {
  Order order = 1;
}

message VoidOrderRequest {
  int64 id = 1;
}

message VoidOrderResponse {
  Order order = 1;
}


message OrderBy {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_ID = 1;
    FIELD_CREATED_AT = 2;
  }

  Field field = 1;
  cloudbeds.rpc.v1.Direction direction = 2;
}

message ListOrdersRequest {
  int64 property_id = 1;
  optional Filter filter = 2;
  repeated OrderBy order_by = 3;
  optional int32 limit = 4;
  optional int32 offset = 5;

  message Filter {
    oneof kind {
      CompositeFilter composite_filter = 1;
      FieldFilter field_filter = 2;
    }
  }

  message CompositeFilter {
    cloudbeds.rpc.v1.LogicalOperator operator = 1;
    repeated Filter filters = 2;
  }

  message FieldFilter {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_ID = 1; // Expecting number value
      FIELD_RESERVATION_ID = 2; // Expecting number value
      FIELD_PROVIDER = 3; //Expecting string value
      FIELD_STATUS = 4; //Expecting string value
    }

    cloudbeds.rpc.v1.ConditionOperator operator = 1;
    Field field = 2;
    cloudbeds.type.v1.Value value = 3;
  }
}

message ListOrdersResponse {
  repeated Order orders = 1;
  optional int32 offset = 2;
  optional int32 total_size = 3;
}

message GetProviderOrderUrlRequest {
  int64 id = 1;
}

message GetProviderOrderUrlResponse {
  string url = 1;
}

message FulfillOrderRequest {
  int64 id = 1;
}

message FulfillOrderResponse {
  Order order = 1;
}
  