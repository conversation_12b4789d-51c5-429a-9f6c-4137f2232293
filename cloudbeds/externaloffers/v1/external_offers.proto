syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "cloudbeds/type/v1/money.proto";

package cloudbeds.externaloffers.v1;

option go_package = "github.com/cloudbeds/protos-go/externaloffers/v1;externaloffers";
option java_package = "com.cloudbeds.externaloffers.v1";
option java_outer_classname = "ExternalOffersProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\ExternalOffers\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\ExternalOffers\\V1\\GPBMetadata";

enum Provider {
  PROVIDER_UNSPECIFIED = 0;
  PROVIDER_HTS = 1;
}

enum SessionEventType {
  SESSION_EVENT_TYPE_UNSPECIFIED = 0;
  SESSION_EVENT_TYPE_BOOKING_CONFIRMED = 1;
}

enum OrderStatus {
  ORDER_STATUS_UNSPECIFIED = 0;
  ORDER_STATUS_CREATED = 1;
  ORDER_STATUS_PROVIDER_ORDER_FAILED = 2;
  ORDER_STATUS_PAYMENT_FORWARDING = 3;
  ORDER_STATUS_PAYMENT_FORWARDING_FAILED = 4;
  ORDER_STATUS_PROVIDER_PURCHASE_FAILED = 5;
  ORDER_STATUS_PREPARED = 6;
  ORDER_STATUS_CONFIRMED = 7;
  ORDER_STATUS_FULFILLED = 8;
  ORDER_STATUS_VOIDED = 9;
  ORDER_STATUS_OFFER_EXPIRED = 10;
}

enum PaymentTypeKind {
  PAYMENT_TYPE_KIND_UNSPECIFIED = 0;
  PAYMENT_TYPE_KIND_TOKEN = 1;
}

message Offer {
  string rate_id = 1;
  string room_id = 2;
  string external_offer_id = 3;
  string name = 4; // "Cancel for any reason (by HTS)"
  cloudbeds.type.v1.Money amount = 5;
}

message Payment {
  cloudbeds.type.v1.Money amount = 1;
  PaymentType payment_type = 2;
}
  
message PaymentType {
  PaymentTypeKind type = 1;
  string value = 2;
}

message Order {
  int64 id = 1;
  Provider provider = 2;
  OrderStatus status = 3;
  int64 reservation_id = 4;
  int64 guest_id = 5;
  string guest_email = 6;
  string guest_locale = 7;
  string currency = 8;
  string payment_method_id = 9;
  string external_order_id = 10;
  string external_payment_token = 11;
  cloudbeds.type.v1.Money external_order_amount = 12;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  google.protobuf.Timestamp prepared_at = 15;
  google.protobuf.Timestamp confirmed_at = 16;
  google.protobuf.Timestamp fulfilled_at = 17;
  google.protobuf.Timestamp voided_at = 18;
  repeated Payment payments = 19;
  repeated OrderProduct products = 20;
}

message OrderProduct {
  int64 id = 1;
  int64 reservation_room_id = 2;
  string external_contract_id = 3;
  string external_offer_id = 4;
  string external_offer_name = 5;
  google.protobuf.Timestamp external_offer_expires_at = 6;
  cloudbeds.type.v1.Money external_product_amount = 7;
  cloudbeds.type.v1.Money room_rate_amount = 8;
  cloudbeds.type.v1.Money addons_amount = 9;
  cloudbeds.type.v1.Money taxes_amount = 10;
  cloudbeds.type.v1.Money fees_amount = 11;
  google.protobuf.Timestamp expires_at = 12;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp confirmed_at = 14;
  google.protobuf.Timestamp exercised_at = 15;
}

message Accommodation {
  int64 reservation_room_id = 1;
  string rate_id = 2;
  string room_id = 3;
  cloudbeds.type.v1.Money room_rate_amount = 4;
  cloudbeds.type.v1.Money addons_amount = 5;
  cloudbeds.type.v1.Money taxes_amount = 6;
  cloudbeds.type.v1.Money fees_amount = 7;
}
  