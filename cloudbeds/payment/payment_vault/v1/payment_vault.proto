syntax = "proto3";

package cloudbeds.payment.payment_vault.v1;

import "cloudbeds/payment/payment_methods/v3/type.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/payment_vault/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentVaultServiceProto";
option java_package = "com.cloudbeds.payment.payment_vault.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\PaymentVaultService\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\PaymentVaultService\\V1";

service PaymentVaultService {
    rpc Tokenize(TokenizeRequest) returns (TokenizeResponse);
    rpc Introspect(IntrospectRequest) returns (IntrospectResponse);
    rpc DeleteToken(DeleteTokenRequest) returns (DeleteTokenResponse);
    rpc Detokenize(DetokenizeRequest) returns (DetokenizeResponse);
    rpc CreateVault(CreateVaultRequest) returns (CreateVaultResponse);
    rpc CreateCredentials(CreateCredentialsRequest) returns (CreateCredentialsResponse);
    rpc CreateProxy(CreateProxyRequest) returns (CreateProxyResponse);
}

message TokenizeRequest {
    string vault = 1;
    PaymentMethod payment_method = 2;
    cloudbeds.payment.payment_methods.v3.BillingDetails billing_details = 3;
}

message TokenizeResponse {
    PaymentMethod payment_method = 1;
}

message PaymentMethod {
    oneof payment_method {
        PaymentMethodCard card = 1;
    }
}

message PaymentMethodCard {
    string cardholder_name = 1;
    string card_number = 2;
    string cvv = 3;
    CardExpiration expiration = 4;
}

message CardExpiration {
    uint64 month = 1;
    uint64 year = 2;
}

message IntrospectRequest {
    string id = 1;
}

message IntrospectResponse {
    Introspection introspect = 1;
}

message Introspection {
    Number number = 1;
    string scheme = 2;
    string type = 3;
    string brand = 4;
    bool prepaid = 5;
    Country country = 6;
    Bank bank = 7;
}

message Number {
    uint64 length = 1;
    bool lunh = 2;
}

message Country {
    string numeric = 1;
    string alpha2 = 2;
    string name = 3;
    string emoji = 4;
    string currency = 5;
    uint64 latitude = 6;
    uint64 longitude = 7;
}

message Bank {
    string name = 1;
    string url = 2;
    string phone = 3;
    string city = 4;
    string country = 5;
}

message DeleteTokenRequest {
    string vault = 1;
    string id = 2;
}

message DeleteTokenResponse {
    string id = 1;
}

message DetokenizeRequest {
    string vault = 1;
    string id = 2;
}

message DetokenizeResponse {
    string vault = 1;
    string id = 2;
    PaymentMethod payment_method = 3;
}

message CreateVaultRequest {
    string name = 1;
}

message CreateVaultResponse {
    string id = 1;
    string name = 2;
}

message CreateCredentialsRequest {
    string vault = 1;
    string scopes = 2;
}

message CreateCredentialsResponse {
    string vault = 1;
    string id = 2;
    string public_key = 3;
    string private_key = 4;
}

message CreateProxyRequest {
    string vault = 1;
    string destination = 2;
    string version = 3;
    oneof schema {
        ProxyJsonSchema json = 4;
    };
}

message CreateProxyResponse {
    string id = 1;
}

message ProxyJsonSchema {
    string cardholder_name = 1;
    string card_number = 2;
    string cvv = 3;
    StringyCardExpiration expiration = 4;
}

message StringyCardExpiration {
    string month = 1;
    string year = 2;
}