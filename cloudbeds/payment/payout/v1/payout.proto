syntax = "proto3";

package cloudbeds.payment.payout.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/payout/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PayoutProto";
option java_package = "com.cloudbeds.payment.payout.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\Payout\\V1\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\Payout\\V1";
option java_generic_services = true;

enum PayoutEventType {
  PAYOUT_EVENT_TYPE_UNSPECIFIED = 0;
  PAYOUT_EVENT_TYPE_SUBMITTED = 1;
  PAYOUT_EVENT_TYPE_UPDATE = 2;
}

message PayoutEvent {
  uint64 id = 1;
  PayoutEventType type = 2;
  uint64 property_id = 3;
  uint64 user_id = 4;
  string event_source = 5;
  google.protobuf.Timestamp event_timestamp = 6;
  oneof payload {
    PayoutSubmittedEvent payout_submitted = 7;
    PayoutUpdatedEvent payout_updated = 8;
  }
}

message PayoutSubmittedEvent {
  Payout payout = 1;
}

message PayoutUpdatedEvent {
  Payout payout = 1;
}

enum PayoutStatus {
  PAYOUT_STATUS_UNSPECIFIED = 0;
  PAYOUT_STATUS_PENDING = 1;
  PAYOUT_STATUS_PAID = 2;
  PAYOUT_STATUS_FAILED = 3;
}

enum PayoutTransactionType {
  PAYOUT_TRANSACTION_TYPE_UNSPECIFIED = 0;
  PAYOUT_TRANSACTION_TYPE_TRANSACTION = 1;
  PAYOUT_TRANSACTION_TYPE_ADJUSTMENT_PROCESSING_FEES = 2;
  PAYOUT_TRANSACTION_TYPE_ADJUSTMENT_PAYOUT_FEE = 3;
}

message PayoutTransaction {
  uint64 id = 1;
  string external_id = 2;
  google.type.Money net_amount = 3;
  google.type.Money fee_amount = 4;
  google.protobuf.Timestamp created_at = 5;
  PayoutTransactionType type = 6;
}

message Payout {
  uint64 property_id = 1;
  string external_id = 2;
  google.type.Money net_amount = 3;
  google.type.Money fee_amount = 4;
  google.type.Money gross_amount = 5;
  string processor = 6;
  string account_id = 7;
  PayoutStatus status = 8;
  google.protobuf.Timestamp created_at = 9;
  repeated PayoutTransaction transactions = 10;
  google.type.Money payout_fee = 11;
}

message PayoutEventKey {
  uint64 property_id = 1;
}
