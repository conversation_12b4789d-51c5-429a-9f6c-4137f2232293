syntax = "proto3";

package cloudbeds.payment.payout.transactions.v1;

import "cloudbeds/type/v1/inventory_object.proto";
import "google/type/money.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/payments-transactions/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentsTransactionsTransactionsProto";
option java_package = "com.cloudbeds.payment.payments_transactions.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\PaymentsTransactions\\V1\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\PaymentsTransactions\\V1";
option java_generic_services = true;

service TransactionsService {
  rpc ListTransactions(ListTransactionsRequest) returns (ListTransactionsResponse);
  rpc CreatePayout(CreatePayoutRequest) returns (CreatePayoutResponse);
}

message Transaction {
  uint64 id = 1;
  cloudbeds.type.v1.InventoryObject inventory_object = 2;
  google.type.Money amount = 3;
  google.protobuf.Timestamp transaction_date = 4;
  google.protobuf.Timestamp settlement_date = 5;
  bool paid_out = 6;
  string inventory_object_name = 7;
  string inventory_object_identifier = 8;
  TransactionType transaction_type = 9;
  optional google.protobuf.Timestamp service_start_date = 10;
  optional google.protobuf.Timestamp service_end_date = 11;

  enum TransactionType {
    TRANSACTION_TYPE_UNSPECIFIED = 0;
    TRANSACTION_TYPE_PURCHASE = 1;
    TRANSACTION_TYPE_AUTHORIZE = 2;
    TRANSACTION_TYPE_CAPTURE = 3;
    TRANSACTION_TYPE_REFUND = 4;
    TRANSACTION_TYPE_VOID = 5;
    TRANSACTION_TYPE_CHARGEBACK = 6;
    TRANSACTION_TYPE_CHARGEBACK_FEE = 7;
    TRANSACTION_TYPE_CHARGEBACK_REVERSAL = 8;
  }
}

message ListTransactionsRequest {
  uint64 property_id = 1;
  optional cloudbeds.type.v1.InventoryObject inventory_object = 2;
  optional google.protobuf.Timestamp start_date = 3;
  optional google.protobuf.Timestamp end_date = 4;
  optional IsPaidOut is_paid_out = 5;

  enum IsPaidOut {
    IS_PAID_OUT_UNSPECIFIED = 0;
    IS_PAID_OUT_YES = 1;
    IS_PAID_OUT_NO = 2;
  }
}

message ListTransactionsResponse {
  repeated Transaction transactions = 1;
}

message CreatePayoutRequest {
  uint64 property_id = 1;
  repeated uint64 transaction_ids = 2;
}

message CreatePayoutResponse {
  uint64 payout_id = 1;
}
