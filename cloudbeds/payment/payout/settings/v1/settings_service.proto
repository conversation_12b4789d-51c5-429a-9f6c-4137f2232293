syntax = "proto3";

package cloudbeds.payment.payout.settings.v1;

option go_package = "github.com/cloudbeds/protos-go/payment/payments-transactions/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentsTransactionsSettingsProto";
option java_package = "com.cloudbeds.payment.payments_transactions.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\PaymentsTransactions\\V1\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\PaymentsTransactions\\V1";
option java_generic_services = true;

service PayoutSettingsService {
  rpc ListSettings(ListSettingsRequest) returns (ListSettingsResponse);
  rpc CreateSettings(CreateSettingsRequest) returns (CreateSettingsResponse);
  rpc UpdateSettings(UpdateSettingsRequest) returns (UpdateSettingsResponse);
  rpc GetPropertySettings(GetPropertySettingsRequest) returns (GetPropertySettingsResponse);
  rpc UpdatePropertySettings(UpdatePropertySettingsRequest) returns (UpdatePropertySettingsResponse);
}

message GetPropertySettingsRequest {
  uint64 property_id = 1;
}

message GetPropertySettingsResponse {
  uint64 property_id = 1;
  bool on_hold = 2;
  uint64 payout_settings_id = 3;
  string timezone = 4;
}

message UpdatePropertySettingsRequest {
  uint64 property_id = 1;
  bool on_hold = 2;
  uint64 payout_settings_id = 3;
}

message UpdatePropertySettingsResponse {
  uint64 property_id = 1;
  bool on_hold = 2;
  uint64 payout_settings_id = 3;
  string timezone = 4;
}

message PayoutSettings {
  string profile_name = 1;
  bool default = 2;
  double reserve = 3;
  double advance = 4;
  int32 distance = 5;
  string payout_frequency = 6;
  int32 initial_hold_days = 7;
}

message SettingsRecord {
  uint64 setting_id = 1;
  PayoutSettings settings = 2;
}

message ListSettingsRequest {
}

message ListSettingsResponse {
  repeated SettingsRecord settings_records = 1;
}

message CreateSettingsRequest {
  PayoutSettings settings = 1;
}

message CreateSettingsResponse {
  SettingsRecord settings_record = 1;
}

message UpdateSettingsRequest {
  SettingsRecord settings_record = 1;
}

message UpdateSettingsResponse {
  SettingsRecord settings_record = 1;
}
