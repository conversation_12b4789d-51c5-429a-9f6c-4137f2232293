syntax = "proto3";

package cloudbeds.payment.v1;

import "cloudbeds/type/v1/inventory_object.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentProto";
option java_package = "com.cloudbeds.payment.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\V1";

enum PaymentStatus {
  PAYMENT_STATUS_UNSPECIFIED = 0;
  PAYMENT_STATUS_SUCCESSFUL = 1;
  PAYMENT_STATUS_SCHEDULED = 2;
  PAYMENT_STATUS_PENDING = 3;
  PAYMENT_STATUS_FAILED = 4;
  PAYMENT_STATUS_CANCELLED = 5;
  PAYMENT_STATUS_UNCONFIRMED = 6;
  PAYMENT_STATUS_EXPIRED = 7;
}

enum CaptureMode {
  CAPTURE_MODE_UNSPECIFIED = 0;
  CAPTURE_MODE_AUTO = 1;
  CAPTURE_MODE_MANUAL = 2;
  CAPTURE_MODE_NONE = 3;
}

enum PaymentMethodDetailsType {
  PAYMENT_METHOD_DETAILS_TYPE_UNSPECIFIED = 0;
  PAYMENT_METHOD_DETAILS_TYPE_CARD = 1;
  PAYMENT_METHOD_DETAILS_TYPE_CASH = 2;
}

message CardPaymentMethodDetails {
  string brand = 1;
  string last4 = 2;
}

message CashPaymentMethodDetails {
  uint64 cash_drawer_id = 1;
  uint64 cash_drawer_session_id = 2;
}

message PaymentMethod {
  uint64 id = 1;
  string code = 2;
  string title = 3;
  optional PaymentMethodDetailsType details_type = 4;
  oneof details {
    CardPaymentMethodDetails card = 5;
    CashPaymentMethodDetails cash = 6;
  }
}

message ProcessingDetails {
  string gateway = 1;
}

message CaptureSummary {
  uint64 id = 1;
  google.type.Money amount = 2;
}

message RefundSummary {
  uint64 id = 1;
  google.type.Money amount = 2;
}

message AdjustmentSummary {
  uint64 id = 1;
  google.type.Money amount = 2;
}

message ChargebackFeeSummary {
  uint64 id = 1;
  google.type.Money amount = 2;
}

message ChargebackSummary {
  uint64 id = 1;
  google.type.Money amount = 2;
  ChargebackFeeSummary fee = 3;
}

message VoidSummary {
  uint64 id = 1;
}

message ChargebackReversalSummary {
  uint64 id = 1;
  google.type.Money amount = 2;
}

message Payment {
  uint64 id = 1;
  uint64 property_id = 2;
  PaymentMethod payment_method = 3;
  google.type.Money amount = 4;
  cloudbeds.type.v1.InventoryObject inventory_object = 5;
  PaymentStatus status = 6;
  CaptureMode capture_mode = 7;
  optional string description = 8;
  map<string, string> metadata = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp processed_at = 11;
  optional ProcessingDetails processing_details = 12;
  repeated CaptureSummary capture_details = 13;
  repeated RefundSummary refund_details = 14;
  repeated AdjustmentSummary adjustment_details = 15;
  optional ConvenienceFee convenience_fee = 16;
  bool is_deposit = 17;
  repeated ChargebackSummary chargeback_details = 18;
  repeated ChargebackReversalSummary chargeback_reversal_details = 19;
  optional VoidSummary void_details = 20;
}

message UnreferencedRefund {
  PaymentMethod payment_method = 1;
  cloudbeds.type.v1.InventoryObject inventory_object = 2;
  CaptureMode capture_mode = 3;
  optional ProcessingDetails processing_details = 4;
}

message Refund {
  uint64 id = 1;
  oneof refund_for {
    Payment payment = 2;
    UnreferencedRefund unreferenced_refund = 3;
  }
  google.type.Money amount = 4;
  PaymentStatus status = 5;
  string description = 6;
  map<string, string> metadata = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp processed_at = 9;
  optional PaymentMethod payment_method = 10;
}

message Capture {
  uint64 id = 1;
  Payment payment = 2;
  google.type.Money amount = 3;
  PaymentStatus status = 4;
  google.protobuf.Timestamp created_at = 5;
  bool is_deposit = 6;
}

message Void {
  uint64 id = 1;
  Payment payment = 2;
  PaymentStatus status = 3;
  google.protobuf.Timestamp created_at = 4;
}

message ChargebackFee {
  uint64 id = 1;
  google.type.Money amount = 2;
}

message Chargeback {
  uint64 id = 1;
  Payment payment = 2;
  google.type.Money amount = 3;
  PaymentStatus status = 4;
  ChargebackFee fee = 5;
  google.protobuf.Timestamp created_at = 6;
  optional PaymentMethod payment_method = 7;
}

message ChargebackReversal {
  uint64 id = 1;
  Payment payment = 2;
  google.type.Money amount = 3;
  PaymentStatus status = 4;
  google.protobuf.Timestamp created_at = 5;
  optional PaymentMethod payment_method = 6;
}

message PaymentAdjustment {
  uint64 id = 1;
  Payment payment = 2;
  google.type.Money amount = 3;
  PaymentStatus status = 4;
  google.protobuf.Timestamp created_at = 99;
}

message ConvenienceFee {
  uint64 id = 1;
  google.type.Money amount = 2;
  string name = 3;
}

enum PaymentProcessorStatus {
  option deprecated = true;
  PAYMENT_PROCESSOR_STATUS_UNSPECIFIED = 0;
  PAYMENT_PROCESSOR_STATUS_VERIFIED = 1;
  PAYMENT_PROCESSOR_STATUS_PENDING = 2;
  PAYMENT_PROCESSOR_STATUS_INCOMPLETE = 3;
  PAYMENT_PROCESSOR_STATUS_DEAUTHORIZED = 4;
  PAYMENT_PROCESSOR_STATUS_CREATED = 5;
  PAYMENT_PROCESSOR_STATUS_REQUESTED = 6;
}
