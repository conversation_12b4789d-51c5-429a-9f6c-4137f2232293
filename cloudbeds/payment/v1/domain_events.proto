syntax = "proto3";

package cloudbeds.payment.v1;

import "google/protobuf/timestamp.proto";
import "cloudbeds/payment/v1/type.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentEventProto";
option java_package = "com.cloudbeds.payment.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\V1";

enum PaymentEventType {
  PAYMENT_EVENT_TYPE_UNSPECIFIED = 0;
  PAYMENT_EVENT_TYPE_PAYMENT_CREATED = 1;
  PAYMENT_EVENT_TYPE_PAYMENT_UPDATED = 2;
  PAYMENT_EVENT_TYPE_PAYMENT_CAPTURED = 3;
  PAYMENT_EVENT_TYPE_CAPTURE_UPDATED = 4;
  PAYMENT_EVENT_TYPE_REFUND_CREATED = 5;
  PAYMENT_EVENT_TYPE_REFUND_UPDATED = 6;
  PAYMENT_EVENT_TYPE_PAYMENT_VOIDED = 7;
  PAYMENT_EVENT_TYPE_CHARGEBACK_CREATED = 8;
  PAYMENT_EVENT_TYPE_CHARGEBACK_UPDATED = 9;
  PAYMENT_EVENT_TYPE_CHARGEBACK_REVERSED = 10;
  PAYMENT_EVENT_TYPE_CHARGEBACK_REVERSAL_UPDATED = 11;
  PAYMENT_EVENT_TYPE_PAYMENT_ADJUSTED = 12;
}

enum PaymentProcessorConfigurationEventType {
  option deprecated = true;
  PAYMENT_PROCESSOR_CONFIGURATION_EVENT_TYPE_UNSPECIFIED = 0;
  PAYMENT_PROCESSOR_CONFIGURATION_EVENT_TYPE_CREATED = 1;
  PAYMENT_PROCESSOR_CONFIGURATION_EVENT_TYPE_UPDATED = 2;
}

message PaymentCreatedEvent {
  Payment payment = 1;
}

message PaymentUpdatedEvent {
  Payment payment = 1;
}

message RefundCreatedEvent {
  Refund refund = 1;
}

message RefundUpdatedEvent {
  Refund refund = 1;
}

message PaymentVoidedEvent {
  Void void = 1;
}

message PaymentCapturedEvent {
  Capture capture = 1;
}

message CaptureUpdatedEvent {
  Capture capture = 1;
}

message ChargebackCreatedEvent {
  Chargeback chargeback = 1;
}

message ChargebackUpdatedEvent {
  Chargeback chargeback = 1;
}

message ChargebackReversedEvent {
  ChargebackReversal chargeback_reversal = 1;
}

message ChargebackReversalUpdatedEvent {
  ChargebackReversal chargeback_reversal = 1;
}

message PaymentAdjustedEvent {
  PaymentAdjustment payment_adjustment = 1;
}

message PaymentProcessorConfiguration {
  string processor = 1;
  bool enabled = 2;
  bool live = 3;
  PaymentProcessorStatus status = 4;
  string external_account_id = 5;
}

message PaymentEvent {
  uint64 id = 1;
  uint64 payment_id = 2;
  PaymentEventType type = 3;
  uint64 property_id = 4;
  uint64 user_id = 5;
  string event_source = 6;
  google.protobuf.Timestamp event_timestamp = 7;
  oneof payload {
    PaymentCreatedEvent payment_created = 8;
    PaymentUpdatedEvent payment_updated = 9;
    PaymentCapturedEvent payment_captured = 10;
    CaptureUpdatedEvent capture_updated = 11;
    RefundCreatedEvent refund_created = 12;
    RefundUpdatedEvent refund_updated = 13;
    PaymentVoidedEvent payment_voided = 14;
    ChargebackCreatedEvent chargeback_created = 15;
    ChargebackUpdatedEvent chargeback_updated = 16;
    ChargebackReversedEvent chargeback_reversed = 17;
    ChargebackReversalUpdatedEvent chargeback_reversal_updated = 18;
    PaymentAdjustedEvent payment_adjusted = 19;
  }
}

message PaymentProcessorConfigurationEvent {
  option deprecated = true;
  uint64 id = 1;
  PaymentProcessorConfigurationEventType type = 2;
  uint64 property_id = 3;
  uint64 user_id = 4;
  string event_source = 5;
  google.protobuf.Timestamp event_timestamp = 6;
  oneof payload {
    PaymentProcessorConfigurationCreatedEvent configuration_created = 7;
    PaymentProcessorConfigurationUpdatedEvent configuration_updated = 8;
  }
}

message PaymentProcessorConfigurationCreatedEvent {
  option deprecated = true;
  PaymentProcessorConfiguration processor_configuration = 1;
}

message PaymentProcessorConfigurationUpdatedEvent {
  option deprecated = true;
  PaymentProcessorConfiguration processor_configuration = 1;
}

message PaymentEventKey {
  uint64 id = 1;
}
