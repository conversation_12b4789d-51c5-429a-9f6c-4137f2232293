syntax = "proto3";

package cloudbeds.payment.v1;

import "cloudbeds/payment/v1/type.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentServiceProto";
option java_package = "com.cloudbeds.payment.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\V1";

service PaymentService {
  rpc GetPayment(GetPaymentRequest) returns (GetPaymentResponse) {}
  rpc GetPaymentsActions(GetPaymentsActionsRequest) returns (GetPaymentsActionsResponse) {}
}

message GetPaymentRequest {
  uint64 property_id = 1;
  uint64 payment_id = 2;
}

message GetPaymentResponse {
  oneof details {
    Payment payment = 1;
    Refund refund = 2;
  }
}

message GetPaymentsActionsRequest {
  uint64 property_id = 1;
  repeated uint64 payment_ids = 2;
}

enum PaymentOperation {
  PAYMENT_OPERATION_UNSPECIFIED = 0;
  PAYMENT_OPERATION_PAYMENT = 1;
  PAYMENT_OPERATION_CAPTURE = 2;
  PAYMENT_OPERATION_REFUND = 3;
  PAYMENT_OPERATION_ADJUSTMENT = 4;
  PAYMENT_OPERATION_VOID = 5;
  PAYMENT_OPERATION_CHARGEBACK = 6;
  PAYMENT_OPERATION_CHARGEBACK_FEE = 7;
  PAYMENT_OPERATION_CHARGEBACK_REVERSAL = 8;
}

message PaymentAction {
  string action = 1;
  map<string, string> parameters = 2;
}

message PaymentOperationEntity {
  uint64 id = 1;
  cloudbeds.payment.v1.PaymentOperation type = 2;
}

message PaymentActions {
  uint64 payment_id = 1;
  PaymentOperationEntity operation = 2;
  repeated PaymentAction actions = 3;
}

message GetPaymentsActionsResponse {
  repeated PaymentActions payments = 1;
}
