syntax = "proto3";

package cloudbeds.payment.payment_methods.v1;

option go_package = "github.com/cloudbeds/protos-go/payment/payment_methods/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentMethodHashProto";
option java_package = "com.cloudbeds.payment.payment_methods.hash.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethods\\Hash\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethods\\Hash\\V1";

service PaymentMethodHashService {
  rpc GetPaymentMethod(GetPaymentMethodRequest) returns (GetPaymentMethodResponse) {}
}

message GetPaymentMethodRequest {
  uint64 property_id = 1;
  string hash = 2;
}

message GetPaymentMethodResponse {
  string payment_method = 1;
}
