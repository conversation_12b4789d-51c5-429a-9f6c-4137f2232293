syntax = "proto3";

import "cloudbeds/payment/payment_methods/v2/type.proto";

package cloudbeds.payment.payment_methods.v2;

option go_package = "github.com/cloudbeds/protos-go/payment/payment_methods/v2;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentMethodsServiceProto";
option java_package = "com.cloudbeds.payment.payment_methods.v2";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethods\\V2\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethods\\V2";

service PaymentMethodsService {
  rpc GetPaymentMethodsDetails(GetPaymentMethodsDetailsRequest) returns (GetPaymentMethodsDetailsResponse) {}
}

message GetPaymentMethodsDetailsRequest {
  uint64 property_id = 1;
  optional uint64 payment_method_id = 2;
  optional string lang_code = 3;
  optional string payment_method_code = 4;
}

message GetPaymentMethodsDetailsResponse {
  repeated PaymentMethodDetails payment_methods = 1;
}
