syntax = "proto3";

package cloudbeds.payment.payment_methods.v2;

import "cloudbeds/type/v1/l10n.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/payment_methods/v2;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentMethodsProto";
option java_package = "com.cloudbeds.payment.payment_methods.v2";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethods\\V2\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethods\\V2";

message PaymentMethodDetails {
  uint64 id = 1;
  string code = 2;
  bool is_active = 3;
  bool is_deleted = 4;
  repeated cloudbeds.type.v1.TranslatedValue translations = 5;
}
