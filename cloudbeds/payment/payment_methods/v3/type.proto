syntax = "proto3";

package cloudbeds.payment.payment_methods.v3;

import "cloudbeds/type/v1/inventory_object.proto";
import "cloudbeds/type/v1/inventory_owner.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/payment_methods/v3;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentMethodsServiceTypesProto";
option java_package = "com.cloudbeds.payment.payment_methods.v3";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethodsService\\V3\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethodsService\\V3";
option java_generic_services = true;

message PaymentMethod {
    string id = 1;
    PaymentMethodType type = 2;
    repeated cloudbeds.type.v1.InventoryObject inventory_object = 3;
    cloudbeds.type.v1.InventoryOwner owner = 4;
    BillingDetails billing_details = 5;
    PaymentMethodSource source = 6;

    oneof method {
        PaymentMethodCard card = 21;
    }
}

message PaymentMethodCard {
  string cardholder_name = 1;
  string card_brand = 2;
  string last4 = 3;
  bool vcc = 4;
  CardExpiration expiration = 5;
  CommercialRule commercial_rule = 6;
  repeated PaymentMethodCardToken tokens = 20;
}

message PaymentMethodCardToken {
    oneof token {
        TokenexToken tokenex = 1;
        StripeToken stripe = 2;
        StripeSingleUseToken stripe_single_use = 3;
        DLocalToken dlocal = 4;
        PaymentsOsToken payments_os = 5;
        PaysafeToken paysafe = 6;
        AdyenToken adyen = 7;
    }
}

enum PaymentMethodType {
    PAYMENT_METHOD_TYPE_UNSPECIFIED = 0;
    PAYMENT_METHOD_TYPE_CARD = 1;
    // ... and others like bank transfer in the future ...
}

enum TokenexVault {
    TOKENEX_VAULT_UNSPECIFIED = 0;
    TOKENEX_VAULT_MYALLOCATOR = 1;
    TOKENEX_VAULT_MYFRONTDESK = 2;
    TOKENEX_VAULT_BLOB = 3;
}

message BillingDetails {
    string address1 = 1;
    string address2 = 2;
    string city = 3;
    string state = 4;
    string postal_code = 5;
    string country = 6;
    string name = 7;
    string document_number = 8;
}

message TokenexToken {
    string token = 1; // ANTOKENAUTO
    string cvv = 2; // GUID
    string reference = 4;
    TokenexVault vault = 5;
}

enum StripeVault {
    STRIPE_VAULT_UNSPECIFIED = 0;
    STRIPE_VAULT_MERCHANT = 1;
    STRIPE_VAULT_PLATFORM = 2;
}

message StripeToken {
    string id = 1;
    StripeVault stripe_vault = 2;
    string customer_id = 3;
}

message StripeSingleUseToken {
    string id = 1;
    StripeVault stripe_vault = 2;
}

message DLocalToken {
    string id = 1;
    string type = 2;
    string cvv = 3;
}

message PaymentsOsToken {
    string id = 1;
}

message PaysafeToken {
    string id = 1;
}

message AdyenToken {
    string version = 1;
    string sr = 2;
    string token = 3;
    string fingerprint = 4;
    string network_transaction = 5;
}

message CardExpiration {
    uint64 month = 1;
    uint64 year = 2;
}

message CommercialRule {
  string version = 1;
  string rule = 2;
}

message PaymentMethodSource {
    PaymentMethodSourceType type = 1;
    string details = 2;
}

enum PaymentMethodSourceType {
    PAYMENT_METHOD_SOURCE_TYPE_UNSPECIFIED = 0;
    PAYMENT_METHOD_SOURCE_TYPE_BOOKINGENGINE = 1;
    PAYMENT_METHOD_SOURCE_TYPE_PAYBYLINK = 2;
    PAYMENT_METHOD_SOURCE_TYPE_MYFRONTDESK = 3;
    PAYMENT_METHOD_SOURCE_TYPE_MYALLOCATOR = 4;
    PAYMENT_METHOD_SOURCE_TYPE_API = 5;
    PAYMENT_METHOD_SOURCE_TYPE_TERMINAL = 6;
    PAYMENT_METHOD_SOURCE_TYPE_IMPORT = 50;
}

enum ForwardingVault {
    FORWARDING_VAULT_UNSPECIFIED = 0;
    FORWARDING_VAULT_SPREEDLY_HTS = 1;
}

message ForwardingCallback {
    ForwardingCallbackType type = 1;
    oneof callback {
        string webhook = 2;
        string event_queue = 3;
    }
}

enum ForwardingCallbackType {
    FORWARDING_CALLBACK_TYPE_UNSPECIFIED = 0;
    FORWARDING_CALLBACK_TYPE_WEBHOOK = 1;
    FORWARDING_CALLBACK_TYPE_EVENT_QUEUE = 2;
}
