syntax = "proto3";

package cloudbeds.payment.payment_methods.v3;

import "cloudbeds/payment/payment_methods/v3/type.proto";
import "cloudbeds/type/v1/inventory_object.proto";
import "cloudbeds/type/v1/inventory_owner.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/payment_methods/v3;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentMethodsServiceProto";
option java_package = "com.cloudbeds.payment.payment_methods.v3";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethodsService\\V3\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\PaymentMethodsService\\V3";
option java_generic_services = true;

service PaymentMethodsService {
    rpc StorePaymentMethod(StorePaymentMethodRequest) returns (StorePaymentMethodResponse);
    rpc GetPaymentMethod(GetPaymentMethodRequest) returns (GetPaymentMethodResponse);
    rpc AssociateCardToken(AssociateCardTokenRequest) returns (AssociateCardTokenResponse);
    rpc UpdateBillingDetails(UpdateBillingDetailsRequest) returns (UpdateBillingDetailsResponse);
    rpc AssociateInventory(AssociateInventoryRequest) returns (AssociateInventoryResponse);
    rpc ForwardPaymentMethod(ForwardPaymentMethodRequest) returns (ForwardPaymentMethodResponse);
}

message StorePaymentMethodRequest {
    PaymentMethod payment_method = 1;
}

message StorePaymentMethodResponse {
    string id = 1;
}

message GetPaymentMethodRequest {
    string id = 1;
    cloudbeds.type.v1.InventoryOwner owner = 2;
    cloudbeds.type.v1.InventoryObject inventory_object = 3;
}

message GetPaymentMethodResponse {
    repeated PaymentMethod payment_method = 20;
}

message AssociateCardTokenRequest {
    string id = 1;
    cloudbeds.type.v1.InventoryOwner owner = 2;
    PaymentMethodCardToken token = 3;
}

message AssociateCardTokenResponse {
    string id = 1;
    bool result = 2;
}

message UpdateBillingDetailsRequest {
    string id = 1;
    BillingDetails billing_details = 2;
}

message UpdateBillingDetailsResponse {
    string id = 1;
    bool result = 2;
}

message AssociateInventoryRequest {
    string id = 1;
    cloudbeds.type.v1.InventoryObject inventory_object = 2;
}

message AssociateInventoryResponse {
    string id = 1;
    bool result = 2;
}

message ForwardPaymentMethodRequest {
    cloudbeds.type.v1.InventoryObject inventory_object = 1;
    ForwardingVault target_vault = 2;
    map<string, string> metadata = 3;
    ForwardingCallback callback = 4;
}

message ForwardPaymentMethodResponse {
}
