syntax = "proto3";

package cloudbeds.payment.payment_configuration.v1;

import "google/protobuf/wrappers.proto";
import "cloudbeds/payment/payment_configuration/v1/type.proto";
import "google/type/date.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/cfg/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentConfigurationProto";
option java_package = "com.cloudbeds.payment.payment_configuration.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\Configuration\\V1\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\Configuration\\V1";
option java_generic_services = true;

service PropertyConfigurationService {
    rpc AddProcessorCredentials(AddProcessorCredentialsRequest) returns (AddProcessorCredentialsResponse);
    rpc GetProcessorCredentials(GetProcessorCredentialsRequest) returns (GetProcessorCredentialsResponse);
    rpc UpdateBillingDetails(UpdateBillingDetailsRequest) returns (UpdateBillingDetailsResponse);
    rpc GetBillingDetails(GetBillingDetailsRequest) returns (GetBillingDetailsResponse);
    rpc ResolveExternalAccount(ResolveExternalAccountRequest) returns (ResolveExternalAccountResponse);
    rpc EnableProcessor(EnableProcessorRequest) returns (EnableProcessorResponse);
    rpc DisableProcessor(DisableProcessorRequest) returns (DisableProcessorResponse);
    rpc GetChannelInstructions(GetChannelInstructionsRequest) returns (GetChannelInstructionsResponse);
    rpc UpdateChannelInstructions(UpdateChannelInstructionsRequest) returns (UpdateChannelInstructionsResponse);
    rpc GetOnboardData(GetOnboardDataRequest) returns (GetOnboardDataResponse);
}

enum Mode {
    MODE_UNSPECIFIED = 0;
    MODE_TEST = 1;
    MODE_LIVE = 2;
}

message ResolveExternalAccountRequest {
    string external_account_id = 1;
    string processor_name = 2;
    Mode mode = 3;
}

message ResolveExternalAccountResponse {
    uint64 property_id = 1;
    string processor_name = 2;
    Mode mode = 3;
}

message GetProcessorCredentialsRequest {
    uint64 property_id = 1;
    Mode mode = 2;
    google.protobuf.BoolValue with_disabled_processors = 3;
}

message GetProcessorCredentialsResponse {
    message Processor {
        string name = 1;
        bool enabled = 2;
        oneof credentials {
            PayPalCredentials paypal_credentials = 3;
            DLocalCredentials dlocal_credentials = 5;
            AuthorizenetCredentials authorizenet_credentials = 8;
            StripeConnectCredentials stripe_connect_credentials = 9;
            StripePlatformCredentials stripe_platform_credentials = 10;
        }

        map<string, string> metadata = 6;
        cloudbeds.payment.payment_configuration.v1.PaymentProcessorStatus status = 7;

        Mode mode = 20;
    }

    message PaymentMethod {
        string name = 1;
        repeated string processors = 2;
    }

    uint64 property_id = 1;
    repeated PaymentMethod payment_method = 2;
    repeated Processor processor = 3;
}

message PayPalCredentials {
    string client_id = 1;
    string client_secret = 2;
}

message StripeConnectCredentials {
    StripeConnectPropertyCredentials property_credentials = 1;
    StripeEnvCredentials env_credentials = 2;
}

message StripePlatformCredentials {
    StripePlatformPropertyCredentials property_credentials = 1;
    StripeEnvCredentials env_credentials = 2;
}

message StripeEnvCredentials {
    string api_key = 1;
    string publishable_key = 2;
}

message StripeConnectPropertyCredentials {
    string account_id = 1;
}

message StripePlatformPropertyCredentials {
    string account_id = 1;
    string country_code = 2;
}

message DLocalCredentials {
    string x_login = 1;
    string x_trans_key = 2;
    string secret_key = 3;
    string country = 4;
    string platform_account_id = 5;
    string account_id = 6;
    string liable_account_id = 7;
    DLocalPropertyCredentials property_credentials = 8;
}

message AuthorizenetCredentials {
    string api_login_id = 1;
    string transaction_key = 2;
}

message DLocalPropertyCredentials {
    string prop_login = 1;
    string prop_trans_key = 2;
    string prop_secret_key = 3;
}

message AddProcessorCredentialsRequest {
    uint64 property_id = 1;
    uint64 processor_id = 2;
    string processor = 3;
    bool enabled = 4;

    oneof credentials {
        PayPalCredentials paypal_credentials = 5;
        DLocalCredentials dlocal_credentials = 7;
        AuthorizenetCredentials authorizenet_credentials = 8;
        StripeConnectCredentials stripe_connect_credentials = 9;
        StripePlatformCredentials stripe_platform_credentials = 10;
    }

    Mode mode = 20;
}

message AddProcessorCredentialsResponse {
    message Processor {
        string name = 1;
        Mode mode = 2;
    }

    message PaymentMethod {
        string name = 1;
        repeated string processors = 2;
    }

    uint64 property_id = 1;
    repeated PaymentMethod payment_method = 2;
    repeated Processor processor = 3;
}

message GetBillingDetailsRequest {
    uint64 property_id = 1;
}

message GetBillingDetailsResponse {
    uint64 property_id = 1;
    BillingAddressRequirements requirements = 2;
    CardConfiguration card_configuration = 3;
    string statement_descriptor = 4;
}

message UpdateBillingDetailsRequest {
    uint64 property_id = 1;
    BillingAddressRequirements requirements = 2;
    CardConfiguration card_configuration = 3;
    string statement_descriptor = 4;
}

message UpdateBillingDetailsResponse {
    uint64 property_id = 1;
    BillingAddressRequirements requirements = 2;
    CardConfiguration card_configuration = 3;
    string statement_descriptor = 4;
}

message BillingAddressRequirements {
    bool be_require_address = 1;
    bool mfd_require_address = 2;
    bool existing_reservation_require_address = 3;
    bool guest_details_as_billing_details = 4;
    bool pbl_require_address_existing_reservation = 5;
}

message CardConfiguration {
    ShowOrRequired country = 1;
    ShowOrRequired postal_code = 2;
    ShowOrRequired address_1 = 3;
    ShowOrRequired address_2 = 4;
    ShowOrRequired city = 5;
    ShowOrRequired state = 6;
    ShowOrRequired email = 7;
    ShowOrRequired name = 8;
    ShowOrRequired phone = 9;
    ShowOrRequired document = 10;
    ShowOrRequired first_name = 11;
    ShowOrRequired last_name = 12;
    ShowOrRequired birthday = 13;
    ShowOrRequired document_type = 14;
    ShowOrRequired tax_id = 15;
    ShowOrRequired company = 16;
    ShowOrRequired fax = 17;
}

message ShowOrRequired {
    bool show = 1;
    bool required = 2;
}

message EnableProcessorRequest {
    uint64 property_id = 1;
    string processor = 2;
}

message EnableProcessorResponse {
}

message DisableProcessorRequest {
    uint64 property_id = 1;
    string processor = 2;
}

message DisableProcessorResponse {
}

message GetChannelInstructionsRequest {
    uint64 property_id = 1;
}

message ChannelInstructions {
    string key = 1;

    message Instruction {
        string type = 1;
        string action = 2;
        uint64 days = 3;
    }

    Instruction channel = 2;
    Instruction property = 3;
}

message GetChannelInstructionsResponse {
    uint64 property_id = 1;
    repeated ChannelInstructions channel_instructions = 2;
}

message UpdateChannelInstructionsRequest {
    uint64 property_id = 1;
    repeated ChannelInstructions channel_instructions = 2;
}

message UpdateChannelInstructionsResponse {
    uint64 property_id = 1;
    repeated ChannelInstructions channel_instructions = 2;
}

message GetOnboardDataRequest {
    uint64 property_id = 1;
}

message GetOnboardDataResponse {
    message Address {
        string country = 1;
        string state = 2;
        string city = 3;
        string line1 = 4;
        string line2 = 5;
        string postal_code = 6;
    }

    message Person {
        Address address = 1;
        string first_name = 2;
        string last_name = 3;
        string email = 4;
        string phone = 5;
        string tax_id = 6;
        string tax_id_type = 7;
        google.type.Date date_of_birth = 8;
        string nationality = 9;
        cloudbeds.payment.payment_configuration.v1.PersonRole role = 10;

    }

    message Bank {
        Address beneficiary_address = 1;
        string beneficiary_account_number = 2;
        string beneficiary_account_type = 3;
        string bank_code = 4;
        int64 bank_id = 5;
        string bank_name = 6;
        string branch_name = 7;
        string branch_code = 8;
        string beneficiary_first_name = 9;
        string beneficiary_last_name = 10;
        string beneficiary_email = 11;
        string beneficiary_phone = 12;
    }

    uint64 property_id = 1;
    Bank bank = 2;
    repeated Person persons = 3;
    string statement_descriptor = 4;
}
