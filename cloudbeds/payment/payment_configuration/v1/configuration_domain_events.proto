syntax = "proto3";

package cloudbeds.payment.payment_configuration.v1;

import "google/protobuf/timestamp.proto";
import "cloudbeds/payment/payment_configuration/v1/type.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/cfg/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentConfigurationDomainEventsProto";
option java_package = "com.cloudbeds.payment.payment_configuration.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\Configuration\\V1\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\Configuration\\V1";
option java_generic_services = true;

enum PaymentProcessorConfigurationEventType {
  PAYMENT_PROCESSOR_CONFIGURATION_EVENT_TYPE_UNSPECIFIED = 0;
  PAYMENT_PROCESSOR_CONFIGURATION_EVENT_TYPE_CREATED = 1;
  PAYMENT_PROCESSOR_CONFIGURATION_EVENT_TYPE_UPDATED = 2;
}

message PaymentProcessorConfiguration {
  string processor = 1;
  bool enabled = 2;
  bool live = 3;
  cloudbeds.payment.payment_configuration.v1.PaymentProcessorStatus status = 4;
  string external_account_id = 5;
}

message PaymentProcessorConfigurationCreatedEvent {
  PaymentProcessorConfiguration processor_configuration = 1;
}

message PaymentProcessorConfigurationUpdatedEvent {
  PaymentProcessorConfiguration processor_configuration = 1;
}

message PaymentProcessorConfigurationEvent {
  uint64 id = 1;
  PaymentProcessorConfigurationEventType type = 2;
  uint64 property_id = 3;
  uint64 user_id = 4;
  string event_source = 5;
  google.protobuf.Timestamp event_timestamp = 6;
  oneof payload {
    PaymentProcessorConfigurationCreatedEvent configuration_created = 7;
    PaymentProcessorConfigurationUpdatedEvent configuration_updated = 8;
  }
}

message PaymentProcessorConfigurationEventKey {
  uint64 property_id = 1;
}
