syntax = "proto3";

package cloudbeds.payment.payment_configuration.v1;

option go_package = "github.com/cloudbeds/protos-go/payment/cfg/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "PaymentConfigurationTypes";
option java_package = "com.cloudbeds.payment.payment_configuration.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\Configuration\\V1\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\Configuration\\V1";
option java_generic_services = true;

enum PaymentProcessorStatus {
  PAYMENT_PROCESSOR_STATUS_UNSPECIFIED = 0;
  PAYMENT_PROCESSOR_STATUS_VERIFIED = 1;
  PAYMENT_PROCESSOR_STATUS_PENDING = 2;
  PAYMENT_PROCESSOR_STATUS_INCOMPLETE = 3;
  PAYMENT_PROCESSOR_STATUS_DEAUTHORIZED = 4;
  PAYMENT_PROCESSOR_STATUS_CREATED = 5;
  PAYMENT_PROCESSOR_STATUS_REQUESTED = 6;
}

enum PersonRole {
  PERSON_ROLE_UNSPECIFIED = 0;
  PERSON_ROLE_OWNER = 1;
  PERSON_ROLE_REPRESENTATIVE = 2;
}