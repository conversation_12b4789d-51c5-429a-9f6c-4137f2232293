syntax = "proto3";

package cloudbeds.payment.payment_configuration.v1;

import "cloudbeds/payment/payment_configuration/v1/configuration.proto";

option go_package = "github.com/cloudbeds/protos-go/payment/cfg/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "TokenexConfigurationProto";
option java_package = "com.cloudbeds.payment.payment_configuration.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Payment\\Configuration\\V1\\Metadata";
option php_namespace = "Cloudbeds\\Protos\\Payment\\Configuration\\V1";
option java_generic_services = true;

service TokenexConfigurationService {
    rpc GenerateIFrameAuthenticationKey(GenerateIFrameAuthenticationKeyRequest) returns (GenerateIFrameAuthenticationKeyResponse);
}

message GenerateIFrameAuthenticationKeyRequest {
    repeated string origin = 1;
    repeated string scheme = 2;
    cloudbeds.payment.payment_configuration.v1.Mode mode = 3;
}

message GenerateIFrameAuthenticationKeyResponse {
    repeated AuthenticationKey authentication_key = 1;
    string client_id = 2;
    string timestamp = 3;
}

message AuthenticationKey {
    string authentication_key = 1;
    string scheme = 2;
}