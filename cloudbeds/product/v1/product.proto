syntax = "proto3";

package cloudbeds.product.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/product/v1;product";
option java_package = "com.cloudbeds.product.v1";
option java_outer_classname = "ProductServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Product\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Product\\V1\\GPBMetadata";

service ProductService {
  rpc GetPropertyProducts(GetPropertyProductsRequest) returns (GetPropertyProductsResponse);
  rpc GetProducts(GetProductsRequest) returns (GetProductsResponse);
}

message GetPropertyProductsRequest {
  int64 property_id = 1;
  repeated int64 product_ids = 2;
}

message GetProductsRequest {
  repeated int64 product_ids = 1;
}

message GetPropertyProductsResponse {
  repeated Product products = 1;
}

// Alias for better naming
message GetProductsResponse {
  repeated Product products = 1;
}

message Product {
  int64 id = 1;
  int64 property_id = 2;
  int64 category_id = 3;
  string sku = 4;
  string product_name = 5;
  string product_code = 6;
  string product_description = 7;
  double product_price = 8;
  bool is_active = 9;
  bool is_required_description = 10;
  int32 product_order = 11;
  string product_type = 12;
  bool do_not_track = 13;
  int32 current_stock = 14;
  int32 reorder_stock = 15;
  int32 stop_selling = 16;
  bool send_notification = 17;
  google.protobuf.Timestamp last_stock_update = 18;
}