syntax = "proto3";

package cloudbeds.policy.v1;

option go_package = "github.com/cloudbeds/protos-go/policy/v1;policy";
option java_package = "com.cloudbeds.policy.v1";
option java_outer_classname = "CodeTableProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Policy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Policy\\V1\\GPBMetadata";

import "cloudbeds/policy/v1/policy_type.proto";

message CodeTable {
    PolicyType policy_type = 1;
    repeated KeyValuePair actions = 2;
    repeated KeyValuePair conditions = 3;
}

message GetCodeTableRequest {
    // The name of the policy type to retrieve a code table for.
    string policy_type_name = 1;
}

message GetCodeTableResponse {
    CodeTable code_table = 1;
}

message KeyValuePair {
    string key = 1;
    string value = 2;
}

service CodeTableService {
    rpc GetCodeTable(GetCodeTableRequest) returns (GetCodeTableResponse);
}
