syntax = "proto3";

package cloudbeds.policy.v1;

option go_package = "github.com/cloudbeds/protos-go/policy/v1;policy";
option java_package = "com.cloudbeds.policy.v1";
option java_outer_classname = "PolicyTypeProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Policy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Policy\\V1\\GPBMetadata";

message PolicyType {
    int64 id = 1;
    string name = 2;
}

message ListPolicyTypesRequest {
    // Optional list of ids to list.
    repeated int64 ids = 1;
}

message ListPolicyTypesResponse {
    // List of policy types.
    repeated PolicyType policy_types = 1;
}

message GetPolicyTypeRequest {
    // Required, id of policy type.
    int64 id = 1;
}

message GetPolicyTypeResponse {
    PolicyType policy_type = 1;
}

service PolicyTypeService {
    rpc ListPolicyTypes(ListPolicyTypesRequest) returns (ListPolicyTypesResponse);
    rpc GetPolicyType(GetPolicyTypeRequest) returns (GetPolicyTypeResponse);
}
