syntax = "proto3";

package cloudbeds.policy.v1;

option go_package = "github.com/cloudbeds/protos-go/policy/v1;policy";
option java_package = "com.cloudbeds.policy.v1";
option java_outer_classname = "EvaluationProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Policy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Policy\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";
import "cloudbeds/policy/v1/rule.proto";

message EvaluateRequest {
    // Required
    uint64 policy_id = 1;

    // Facts to use during evaluation.
    map<string, string> facts = 2;
}

message EvaluateResponse {
    repeated RuleAction actions = 1;
    google.protobuf.Timestamp evaluation_time = 2;
}

service EvaluationService {
    rpc Evaluate(EvaluateRequest) returns (EvaluateResponse);
}
