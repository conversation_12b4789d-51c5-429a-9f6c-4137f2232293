syntax = "proto3";

package cloudbeds.policy.v1;

option go_package = "github.com/cloudbeds/protos-go/policy/v1;policy";
option java_package = "com.cloudbeds.policy.v1";
option java_outer_classname = "RuleProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Policy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Policy\\V1\\GPBMetadata";

message Rule {
    int64 id = 1;
    int32 priority = 2;
    repeated RuleAction actions = 3;
    repeated RuleCondition conditions = 4;
}

message RuleCondition {
    int64 id = 1;
    Condition condition = 2;
    string condition_value = 3;
}

message Condition {
    int64 id = 1;
    string name = 2;
    bool value_required = 3;
}

message RuleAction {
    int64 id = 1;
    Action action = 2;
    string action_value = 3;
}

message Action {
    int64 id = 1;
    string name = 2;
    bool value_required = 3;
}

message CreateRuleRequest {
    int32 priority = 1;
    repeated CreateRuleActionRequest actions = 2;
    repeated CreateRuleConditionRequest conditions = 4;
}

message CreateRuleConditionRequest {
    string condition = 1;
    string value = 2;
}

message CreateRuleActionRequest {
    string action = 1;
    string value = 2;
}
