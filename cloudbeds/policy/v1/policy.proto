syntax = "proto3";

package cloudbeds.policy.v1;

option go_package = "github.com/cloudbeds/protos-go/policy/v1;policy";
option java_package = "com.cloudbeds.policy.v1";
option java_outer_classname = "PolicyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Policy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Policy\\V1\\GPBMetadata";

import "cloudbeds/policy/v1/policy_type.proto";
import "cloudbeds/policy/v1/rule.proto";

message Policy {
    uint64 id = 1;
    PolicyType policy_type = 2;
    repeated Rule rules = 3;
}

message ListPoliciesRequest {
    repeated uint64 ids = 1;
}

message ListPoliciesResponse {
    repeated Policy policies = 1;
}

message CreatePolicyRequest {
    string policy_type_name = 1;
    repeated CreateRuleRequest rules = 2;
}

// Request to create policies in batch.
// NOTE: This request requires an x-property-id header to be set.
message BatchCreatePoliciesRequest {
    repeated CreatePolicyRequest policies = 1;
}

// Response for creating policies in batch.
message BatchCreatePoliciesResponse {
    repeated Policy policies = 1;
}

service PolicyService {
    rpc ListPolicies(ListPoliciesRequest) returns (ListPoliciesResponse);
    rpc BatchCreatePolicies(BatchCreatePoliciesRequest) returns (BatchCreatePoliciesResponse);
}
