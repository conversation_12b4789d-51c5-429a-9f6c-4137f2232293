syntax = "proto3";

package cloudbeds.guestrequirements.v1;

option go_package = "github.com/cloudbeds/protos-go/guestrequirements/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "GuestRequirementsProto";
option java_package = "com.cloudbeds.guestrequirements.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\GuestRequirements\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\GuestRequirements\\V1";

service GuestRequirementsService {
  rpc ReadPropertyRequirements (ReadPropertyRequirementsRequest) returns (ReadPropertyRequirementsResponse);
  rpc ReadGuestRequirements (ReadGuestRequirementsRequest) returns (ReadGuestRequirementsResponse);
  rpc ListGuestRequirements (ListGuestRequirementsRequest) returns (ListGuestRequirementsResponse);
  rpc CreateGuestRequirements (CreateGuestRequirementsRequest) returns (CreateGuestRequirementsResponse);
  rpc ValidateRequirements (ValidateRequirementsRequest) returns (ValidateRequirementsResponse);
}

enum EntityType {
  ENTITY_TYPE_UNSPECIFIED = 0; // Default value
  ENTITY_TYPE_GUEST = 1; // Guest entity
}

enum AppliesTo {
  APPLIES_TO_UNSPECIFIED = 0; // Default value
  APPLIES_TO_POLICE_REPORT = 1; // Police report requirements
  APPLIES_TO_INVOICE = 2; // Invoice requirements
}

message GuestRequirementsField {
  string name = 1;
  string value = 2;
}

message GuestRequirements {
  int64 property_id = 1;
  int64 guest_id = 2;
  repeated GuestRequirementsField fields = 3;
}

message ReadGuestRequirementsRequest {
  int64 property_id = 1;
  int64 guest_id = 2;
}

message ReadGuestRequirementsResponse {
  GuestRequirements guest_requirements = 1;
}

message ListGuestRequirementsRequest {
  int64 property_id = 1;
  repeated int64 guest_ids = 2;
}

message ListGuestRequirementsResponse {
  repeated GuestRequirements guest_requirements = 1;
}

message CreateGuestRequirementsRequest {
  int64 property_id = 1;
  int64 guest_id = 2;
  repeated GuestRequirementsField fields = 3;
}

message CreateGuestRequirementsResponse {
  GuestRequirements guest_requirements = 1;
}

message PropertyRequirementsField {
  string name = 1;
  string type = 2;
}

enum TemplateType {
  TEMPLATE_TYPE_UNSPECIFIED = 0; 
  TEMPLATE_TYPE_GUEST = 1; 
  TEMPLATE_TYPE_COMPANY = 2; 
}

message ReadPropertyRequirementsRequest {
  int64 property_id = 1;
  optional TemplateType template_type = 2; // Default is GUEST
}

message ReadPropertyRequirementsResponse {
  int64 property_id = 1;
  optional int64 template_id = 2;
  repeated PropertyRequirementsField fields = 3;
}

message ValidateRequirementsRequest {
  int64 property_id = 1;
  optional string locale = 2; // Default is "en-US"
  EntityType entity_type = 3; // Default is GUEST
  int64 entity_id = 4; // ID of the guest or company
  repeated AppliesTo applies_to = 5; // optional
}

message RequirementError {
  string field = 1; // The field that has the error ex. "firstName"
  string name = 2; // The i18n name of the field ex. "First Name"
  string message = 3; // The error message 'Field "First Name" is required'
}

message ValidateRequirementsResponse {
  bool valid = 1; // true if requirements are met
  repeated RequirementError errors = 2; // List of errors if requirements are not met
}