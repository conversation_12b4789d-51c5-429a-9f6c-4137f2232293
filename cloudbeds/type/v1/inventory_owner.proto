syntax = "proto3";

package cloudbeds.type.v1;

option go_package = "github.com/cloudbeds/protos-go/type/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "InventoryOwnerProto";
option java_package = "com.cloudbeds.type.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Type\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Type\\V1";

enum InventoryOwnerType {
  INVENTORY_OWNER_TYPE_UNSPECIFIED = 0;
  INVENTORY_OWNER_TYPE_PROPERTY = 1;
  // organization, guest, etc... for future
}

message InventoryOwner {
  uint64 id = 1;
  InventoryOwnerType type = 2;
}
