syntax = "proto3";

package cloudbeds.type.v1;

option go_package = "github.com/cloudbeds/protos-go/type/v1;cloudbeds";
option java_package = "com.cloudbeds.type.v1";
option java_outer_classname = "StructProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Type\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Type\\V1\\GPBMetadata";

// NOTE: This is a copy of https://github.com/protocolbuffers/protobuf/blob/c7d469748188a17716f1de5a025324db933f5730/src/google/protobuf/struct.proto
//       with some additions to Value to natively support snowflake ids.

// `Struct` represents a structured data value, consisting of fields
// which map to dynamically typed values. In some languages, `Struct`
// might be supported by a native representation. For example, in
// scripting languages like JS a struct is represented as an
// object. The details of that representation are described together
// with the proto support for the language.
//
// The JSON representation for `Struct` is JSON object.
message Struct {
    // Unordered map of dynamically typed values.
    map<string, Value> fields = 1;
  }
  
  // `Value` represents a dynamically typed value which can be either
  // null, a number, a string, a boolean, a recursive struct value, or a
  // list of values. A producer of value is expected to set one of these
  // variants. Absence of any variant indicates an error.
  //
  // The JSON representation for `Value` is JSON value.
  message Value {
    // The kind of value.
    oneof kind {
      // Represents a null value.
      NullValue null_value = 1;
      // Represents a double value.
      double number_value = 2;
      // Represents a string value.
      string string_value = 3;
      // Represents a boolean value.
      bool bool_value = 4;
      // Represents a structured value.
      Struct struct_value = 5;
      // Represents a repeated `Value`.
      ListValue list_value = 6;
      // Represents a int64 value.
      int64 int64_value = 7;
      // Represents an uint64 value.
      uint64 uint64_value = 8;
    }
  }
  
  // `NullValue` is a singleton enumeration to represent the null value for the
  // `Value` type union.
  //
  // The JSON representation for `NullValue` is JSON `null`.
  enum NullValue {
    // Null value.
    NULL_VALUE = 0;
  }
  
  // `ListValue` is a wrapper around a repeated field of values.
  //
  // The JSON representation for `ListValue` is JSON array.
  message ListValue {
    // Repeated field of dynamically typed values.
    repeated Value values = 1;
  }
