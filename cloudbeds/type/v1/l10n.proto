syntax = "proto3";

package cloudbeds.type.v1;

option go_package = "github.com/cloudbeds/protos-go/type/v1;cloudbeds";
option java_package = "com.cloudbeds.type.v1";
option java_outer_classname = "L10nProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Type\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Type\\V1\\GPBMetadata";

message TranslatedValue {
  string lang_code = 1;
  string value = 2;
}

enum Language {
    LANGUAGE_UNSPECIFIED = 0;
    LANGUAGE_EN_US = 1;
    LANGUAGE_ES_MX = 2;
    LANGUAGE_ES_ES = 3;
    LANGUAGE_PT_BR = 4;
    LANGUAGE_PT_PT = 5;
    LANGUAGE_RU_RU = 6;
    LANGUAGE_TH_TH = 7;
    LANGUAGE_DE_DE = 8;
    LANGUAGE_IT_IT = 9;
    LANGUAGE_FR_FR = 10;
    LANGUAGE_AR_SA = 11;
    LANGUAGE_CA_ES = 12;
    LANGUAGE_ID_ID = 13;
    LANGUAGE_ACH = 14;
}
