syntax = "proto3";

package cloudbeds.type.v1;

option go_package = "github.com/cloudbeds/protos-go/type/v1;cloudbeds";
option java_package = "com.cloudbeds.type.v1";
option java_outer_classname = "MoneyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Type\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Type\\V1\\GPBMetadata";

// Represents an amount of money with its currency type.
message Money {
  // The three-letter currency code defined in ISO 4217.
  string currency_code = 1;

  // Amount is represented in the smallest unit (eg. cents)
  // For example, $-5.25 USD is represented as `currency_code`=USD, `amount`=-525.
  int64 amount = 2;
}
