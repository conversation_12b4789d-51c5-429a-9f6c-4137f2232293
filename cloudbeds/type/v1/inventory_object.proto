syntax = "proto3";

package cloudbeds.type.v1;

option go_package = "github.com/cloudbeds/protos-go/type/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "InventoryObjectProto";
option java_package = "com.cloudbeds.type.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Type\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Type\\V1";

enum InventoryObjectType {
  INVENTORY_OBJECT_TYPE_UNSPECIFIED = 0;
  INVENTORY_OBJECT_TYPE_RESERVATION = 1;
  INVENTORY_OBJECT_TYPE_CITY_LEDGER = 2; //What's instead of city ledger?  Renamed to accounts receivable ledger
  INVENTORY_OBJECT_TYPE_HOUSE_ACCOUNT = 3;
  INVENTORY_OBJECT_TYPE_GROUP = 4;
  INVENTORY_OBJECT_TYPE_GUEST = 5;
  INVENTORY_OBJECT_TYPE_ACCOUNTS_RECEIVABLE_LEDGER = 6;
  INVENTORY_OBJECT_TYPE_RESERVATION_ROOM = 7;
}

message InventoryObject {
  uint64 id = 1;
  InventoryObjectType type = 2;
}
