syntax = "proto3";

package cloudbeds.kafka.common.v1;

option go_package = "github.com/cloudbeds/protos-go/kafka/common/v1;common";
option java_package = "com.cloudbeds.kafka.common.v1";
option java_outer_classname = "KafkaKeyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Kafka\\Common\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Kafka\\Common\\V1\\GPBMetadata";

message KafkaKey {
  string id = 1; 
}