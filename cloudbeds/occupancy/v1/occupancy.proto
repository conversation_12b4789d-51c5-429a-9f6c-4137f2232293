syntax = "proto3";

package cloudbeds.occupancy.v1;

import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/l10n.proto";

option go_package = "github.com/cloudbeds/protos-go/occupancy/v1;occupancy";
option java_package = "com.cloudbeds.occupancy.v1";
option java_outer_classname = "OccupancyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Occupancy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Occupancy\\V1\\GPBMetadata";

import "google/type/date.proto";

enum Column {
    COLUMN_UNSPECIFIED = 0;

    COLUMN_STAY_DATE = 1;
    COLUMN_ORGANIZATION_ID = 2;
    COLUMN_PROPERTY_ID = 3;
    COLUMN_ROOM_TYPE_ID = 4;
    COLUMN_ROOM_TYPE = 5;
    //  REMOVED: COLUMN_ROOM_ID = 6;
    COLUMN_ROOM_TYPE_SHORT_TITLE = 7;
    //  REMOVED: COLUMN_ROOM_NAME = 8;
    COLUMN_GROUP_PROFILE_ID = 9;
    COLUMN_GROUP_PROFILE = 10;
    COLUMN_GROUP_PROFILE_CODE = 11;

    // Metrics
    COLUMN_OCCUPANCY = 20;
    COLUMN_CAPACITY_COUNT = 21;
    COLUMN_TOTAL_ROOMS_SOLD = 22;              // Number of rooms sold
    COLUMN_TOTAL_ROOMS_AVAILABLE = 23;         // Number of rooms available
    COLUMN_BLOCKED_ROOMS_COUNT = 24;           // Number of rooms blocked
    COLUMN_OUT_OF_SERVICE_COUNT = 25;          // Number of rooms marked as out of service
    COLUMN_TOTAL_ROOM_RATE = 26;               // Total room rate per night
    COLUMN_TOTAL_ROOM_REVENUE = 27;            // Total revenue from room bookings
    COLUMN_TOTAL_OTHER_ROOM_REVENUE = 28;      // Revenue from other room-related charges
    COLUMN_TOTAL_ROOM_REVENUE_ADJUSTMENTS = 29; // Total adjustments to room revenue
    COLUMN_TOTAL_ROOM_TAXES = 30;              // Total taxes applied to the booking
    COLUMN_TOTAL_ROOM_FEES = 31;               // Total fees applied to the booking
    COLUMN_TOTAL_OTHER_REVENUE = 32;           // Revenue from non-room sources
    COLUMN_TOTAL_REVENUE = 33;                 // Revenue from non-room sources
    COLUMN_REVPAR = 34;                        // Revenue per available room
    COLUMN_ADR = 35;                           // Average Daily Rate
    COLUMN_GUEST_COUNT = 36;                   // Total number of guests in the room
    COLUMN_ADULTS_COUNT = 37;                  // Number of adults in the room
    COLUMN_CHILDREN_COUNT = 38;                // Number of children in the room
}

enum Granularity {
    GRANULARITY_UNSPECIFIED = 0;
    GRANULARITY_DAY = 1;
    GRANULARITY_MONTH = 2;
    GRANULARITY_YEAR = 3;
}

enum GroupBy {
    GROUP_BY_UNSPECIFIED = 0;
    GROUP_BY_ROOM_TYPE = 1;
    GROUP_BY_STAY_DATE = 2;
    //  REMOVED: GROUP_BY_RESERVATION_SOURCE = 3;
    GROUP_BY_GROUP_PROFILE_CODE = 4;
}

service OccupancyService {
    rpc Query (QueryRequest) returns (QueryResponse);
    rpc QueryGroupProfile (QueryGroupProfileRequest) returns (QueryGroupProfileResponse);
}

message OrderBy {
    enum Field {
        FIELD_UNSPECIFIED = 0;
        FIELD_ADR = 1;          // Average Daily Rate
    }

    Field field = 1;
    cloudbeds.rpc.v1.Direction direction = 2;
}

message QueryRequest {
    int64 organization_id = 1;
    repeated int64 property_ids = 2;
    
    google.type.Date start_date = 3;
    google.type.Date end_date = 4;

    repeated Column columns = 5;
    repeated GroupBy group_by = 6;
    optional Granularity granularity = 7;
    repeated int64 room_type_ids = 8;
    optional cloudbeds.type.v1.Language language = 9;

    uint32 limit = 100;
    uint32 offset = 101;
    repeated OrderBy order_by = 102;
}


// Data has a property per column. Only those properties that were 
// requested via "columns" in the query will be filled.
// Grouped columns will also be omitted. These will be present in the grouping
// keys.
message Data {
    optional google.type.Date stay_date = 1;            // COLUMN_STAY_DATE = 1;
    optional uint64 organization_id = 2;                // COLUMN_ORGANIZATION_ID = 2;
    optional uint64 property_id = 3;                    // COLUMN_PROPERTY_ID = 3;
    optional uint64 room_type_id = 4;                   // COLUMN_ROOM_TYPE_ID = 4;
    optional string room_type = 5;                      // COLUMN_ROOM_TYPE = 5;
    //  REMOVED: optional string room_id = 6;                        // COLUMN_ROOM_ID = 6;
    optional string room_type_short_title = 7;          // COLUMN_ROOM_TYPE_SHORT_TITLE = 7;
    //  REMOVED: optional string room_name = 8;                      // COLUMN_ROOM_NAME = 8;
    optional uint64 group_profile_id = 9;               // COLUMN_GROUP_PROFILE_ID = 9;
    optional string group_profile = 10;                 // COLUMN_GROUP_PROFILE = 10;
    optional string group_profile_code = 11;            // COLUMN_GROUP_PROFILE_CODE = 11;
    
    // Metrics
    optional float occupancy = 20;                      // COLUMN_OCCUPANCY = 20;
    optional uint32 capacity_count = 21;                // COLUMN_CAPACITY_COUNT = 21;
    optional uint32 total_rooms_sold = 22;              // COLUMN_TOTAL_ROOMS_SOLD = 22;
    optional uint32 total_rooms_available = 23;         // COLUMN_TOTAL_ROOMS_AVAILABLE = 23;
    optional uint32 blocked_rooms_count = 24;           // COLUMN_BLOCKED_ROOMS_COUNT = 24;
    optional uint32 out_of_service_count = 25;          // COLUMN_OUT_OF_SERVICE_COUNT = 25;
    optional float total_room_rate = 26;                // COLUMN_TOTAL_ROOM_RATE = 26;
    optional float total_room_revenue = 27;             // COLUMN_TOTAL_ROOM_REVENUE = 27;
    optional float total_revenue = 28;                  // COLUMN_TOTAL_REVENUE = 28;
    optional float total_other_room_revenue = 29;       // COLUMN_TOTAL_OTHER_ROOM_REVENUE = 29;
    optional float total_room_revenue_adjustments = 30; // COLUMN_TOTAL_ROOM_REVENUE_ADJUSTMENTS = 30;
    optional float total_room_taxes = 31;               // COLUMN_TOTAL_ROOM_TAXES = 31;
    optional float total_room_fees = 32;                // COLUMN_TOTAL_ROOM_FEES = 32;
    optional float total_other_revenue = 33;            // COLUMN_TOTAL_OTHER_REVENUE = 33;
    optional float revpar = 34;                         // COLUMN_REVPAR = 34;
    optional float adr = 35;                            // COLUMN_ADR = 35;
    optional uint32 guest_count = 36;                   // COLUMN_GUEST_COUNT = 36;
    optional uint32 adults_count = 37;                  // COLUMN_ADULTS_COUNT = 37;
    optional uint32 children_count = 38;                // COLUMN_CHILDREN_COUNT = 38;
}

message QueryResponse {
    // Grouped
    // {'2025-01': {
    //   'type a': {'Booking.com': {'adr': 1.0, 'revpar': 0.5}}, 
    //   'type b': {'Walk In': {'adr': 2.0, 'revpar': 0.75}}}, 
    //  '2025-02': {
    //   'type a': {'Booking.com': {'adr': 30.0, 'revpar': 0.15}}, 
    //   'type b': {'Walk In': {'adr': 40.0, 'revpar': 0.1}}}}

    // Ungrouped
    // [{'revpar': 0.50, 'adr': 1.25, 'stay_date': '2024-01-01', 'occupancy_rate': .50, 'rooms_sold': 50}
    //  {'revpar': 1.50, 'adr': 5.25, 'stay_date': '2024-01-02', 'occupancy_rate': .75, 'rooms_sold': 3}]
    
    message ListOfData {
        repeated Data data = 1;
    }

    message Summary {
        map<string, Grouped> groups = 1;
    }
    
    message Grouped {
        oneof grouped {
            Summary sub_groups = 1;
            Data data = 2;
        }
    }

    oneof records {
        ListOfData list = 1;
        Summary summary = 2;
    }

    uint32 limit = 100;
    uint32 offset = 101;
}

message QueryGroupProfileRequest {
    int64 property_id = 1;
    int64 group_profile_id = 2;
}

message QueryGroupProfileResponse {
    float adr = 1;
    float total_room_revenue = 2;
    uint32 total_rooms_sold = 3;
    uint32 guest_count = 4;
}
