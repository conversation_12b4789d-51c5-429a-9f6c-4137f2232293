syntax = "proto3";

package cloudbeds.activitylog.v1;

option go_package = "github.com/cloudbeds/protos-go/activitylog/v1;activitylog";
option java_package = "com.cloudbeds.activitylog.v1";
option java_outer_classname = "MessageProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Activitylog\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Activitylog\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";
import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";

// An API client entity, such as "whistle_client" or any other defined API client identifier
message ApiClient {
  string id = 1;
  string name = 2;
}

// Represents a user initiating the activity, including optional email and IP address fields
message User {
  uint64 id = 1;
  string name = 2;
  string email = 3; // Optional field for user's email address
  string ip = 4;    // Optional field for user's IP address
}

// Metadata about the content involved in the activity, including a type and an optional identifier
message Content {
  string type = 1; // Example types: "addon", "product"
  string id = 2;   // Optional unique identifier for the content
}

// Describes the action performed, such as "create" or "modify"
message Action {
  string type = 1; // Examples: "create", "modify"
}

// Identifies the source of the activity, such as "calendar" or "cash_drawer"
message Source {
  string type = 1; // Examples: "calendar", "cash_drawer"
}

// Information about a specific field involved in the activity
message Field {
  string type = 1; // Examples: "compset", "restriction"
}

// Encapsulates data in JSON format; stored as a string to allow large data handling and compression
// Example: {"room_type": 1000, "room_identifier": 123, "room_name": "Deluxe Queen", "start_date": "2023-01-01", "end_date": "2023-01-03"}
message JsonFormattedData {
  string new_value = 1; // The updated value in JSON format
  string old_value = 2; // Optional field for the previous value in JSON format
}

// Represents information about a property in the activity log
message Property {
  uint64 id = 1;
}

// Contains additional context for the activity, which could be plain text or a JSON string
message Context {
  string value = 1; // Examples: "Room 123 updated to Room 456" or '["Room 123", "Room 456"]'
}

// The main structure for an activity log entry
message ActivityLog {
  uint64 id = 1;                              // Optional unique identifier for the activity log entry
  Property property = 2;                      // The property associated with this activity
  User user = 3;                              // The user performing the activity
  Action action = 4;                          // The action being performed
  Source source = 5;                          // The source of the activity
  Content content = 6;                        // The content involved in the activity
  JsonFormattedData json_formatted_data = 7;  // The data changes in JSON format
  ApiClient api_client = 8;                   // Optional reference to the API client
  google.protobuf.Timestamp date = 9;         // Optional timestamp; uses the current time if not set
  Context context = 10;                       // Optional contextual information about the activity
  Field field = 11;                           // Optional field information
  bool write_to_shadow_store = 12;            // Optional flag to write the new data to storage (default: false)
}

// Request message for creating a new activity log entry
message CreateActivityLogRequest {
  ActivityLog activity_log = 1; // The activity log entry to be created
}

// Response message for a successful CreateActivityLogRequest
message CreateActivityLogResponse {
}

message SearchActivityLogRequest {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_DATE = 1;
    FIELD_PROPERTY_ID = 2;
    FIELD_USER_ID = 3;
    FIELD_USER_NAME = 4;
    FIELD_DATA_ACTION = 5;
    FIELD_DATA_SOURCE = 6;
    FIELD_DATA_CONTENT = 7;
    FIELD_DATA_FIELD = 8;
    FIELD_DATA_OLD = 9;
    FIELD_DATA_NEW = 10;
    FIELD_RESULT = 11;
    FIELD_STATUS = 12;
    FIELD_API_CLIENT_ID = 13;
  }

  message CompositeFilter {
    cloudbeds.rpc.v1.LogicalOperator operator = 1;
    repeated Filter filters = 2;
  }

  message FieldFilter {
    cloudbeds.rpc.v1.ConditionOperator operator = 1;
    Field field = 2;
    cloudbeds.type.v1.Value value = 3;
  }

  message Pagination {
    uint32 limit = 1;
    uint32 offset = 2;
  }

  message Filter {
    oneof kind {
      CompositeFilter composite_filter = 1;
      FieldFilter filter = 2;
    }
  }

  message OrderBy {
    Field field = 1;
    cloudbeds.rpc.v1.Direction direction = 2;
  }

  Filter filter = 1;
  repeated OrderBy order_by = 2;
  Pagination pagination = 3;
}

message SearchActivityLogResponse {
  repeated ActivityLog activity_logs = 1;
}
