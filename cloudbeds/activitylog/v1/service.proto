syntax = "proto3";

package cloudbeds.activitylog.v1;

option go_package = "github.com/cloudbeds/protos-go/activitylog/v1;activitylog";
option java_package = "com.cloudbeds.activitylog.v1";
option java_outer_classname = "ActivityLogServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Activitylog\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Activitylog\\V1\\GPBMetadata";

import "cloudbeds/activitylog/v1/message.proto";

service ActivityLogService {
  rpc CreateActivityLog(CreateActivityLogRequest) returns (CreateActivityLogResponse);
  rpc SearchActivityLog(SearchActivityLogRequest) returns (SearchActivityLogResponse);
}
