syntax = "proto3";

package cloudbeds.permission.di.v1;

import "cloudbeds/permission/di/v1/types.proto";
import "cloudbeds/permission/v1/types.proto";

option go_package = "github.com/cloudbeds/protos-go/permission/di/v1;permission";
option java_package = "com.cloudbeds.permission.di.v1";
option java_outer_classname = "PermissionApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Permission\\DI\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Permission\\DI\\V1\\GPBMetadata";

service PermissionService {
  rpc ListPermissions (ListPermissionsRequest) returns (ListPermissionsResponse);
  rpc CheckPermission (CheckPermissionRequest) returns (CheckPermissionResponse);
}

message ListPermissionsRequest {
  cloudbeds.permission.v1.SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  cloudbeds.permission.v1.ScopeType.Scope scope_type = 3;
  optional int64 scope_id = 4;
  repeated Permission permissions = 5;
}

message ListPermissionsResponse {
  repeated Permission permissions = 1;
}

message CheckPermissionRequest {
  cloudbeds.permission.v1.SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  cloudbeds.permission.v1.ScopeType.Scope scope_type = 3;
  optional int64 scope_id = 4;
  repeated Permission permissions = 5;
}

message CheckPermissionResponse {
  repeated PermissionActionAccess permissions = 1;
}

message PermissionActionAccess {
  string resource = 1;
  repeated ActionAccess actions = 2;
}

message ActionAccess {
  ActionType.Action action = 1;
  bool access = 2;
}

message Permission {
  string resource = 1;
  repeated ActionType.Action actions = 2;
}
