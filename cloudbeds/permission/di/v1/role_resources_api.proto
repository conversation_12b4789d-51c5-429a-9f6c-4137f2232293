syntax = "proto3";

package cloudbeds.permission.di.v1;

import "cloudbeds/permission/di/v1/types.proto";
import "cloudbeds/permission/v1/types.proto";

option go_package = "github.com/cloudbeds/protos-go/permission/di/v1;permission";
option java_package = "com.cloudbeds.permission.di.v1";
option java_outer_classname = "RoleResourcesApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Permission\\DI\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Permission\\DI\\V1\\GPBMetadata";

service RoleResourcesService {
  rpc ListRoleResources (ListRoleResourcesRequest) returns (ListRoleResourcesResponse);
  rpc ChangeRoleResources (ChangeRoleResourcesRequest) returns (ChangeRoleResourcesResponse);
  rpc AssignRoleResources (AssignRoleResourcesRequest) returns (AssignRoleResourcesResponse);
  rpc RevokeRoleResources (RevokeRoleResourcesRequest) returns (RevokeRoleResourcesResponse);
}

message ListRoleResourcesRequest {
  cloudbeds.permission.v1.SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  cloudbeds.permission.v1.ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  repeated string resource = 5;
}

message ListRoleResourcesResponse {
  repeated RoleResource role_resources = 1;
}

message ChangeRoleResourcesRequest {
  cloudbeds.permission.v1.SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  cloudbeds.permission.v1.ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  repeated RoleResource role_resources = 5;
}

message ChangeRoleResourcesResponse {
  bool status = 1;
}

message AssignRoleResourcesRequest {
  cloudbeds.permission.v1.SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  cloudbeds.permission.v1.ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  repeated RoleResource role_resources = 5;
}

message AssignRoleResourcesResponse {
  bool status = 1;
}

message RevokeRoleResourcesRequest {
  cloudbeds.permission.v1.SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  cloudbeds.permission.v1.ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  repeated RoleResource role_resources = 5;
}

message RevokeRoleResourcesResponse {
  bool status = 1;
}

message RoleResource {
  string resource = 1;
  RoleType.Role role = 2;
}
