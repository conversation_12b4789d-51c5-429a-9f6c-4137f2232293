syntax = "proto3";

package cloudbeds.permission.di.v1;

option go_package = "github.com/cloudbeds/protos-go/permission/di/v1;permission";
option java_package = "com.cloudbeds.permission.di.v1";
option java_outer_classname = "DiTypesProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Permission\\DI\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Permission\\DI\\V1\\GPBMetadata";

message RoleType {
  enum Role {
    ROLE_UNSPECIFIED = 0;
    ROLE_DI_VIEWER = 1;
    ROLE_DI_AUTHOR = 2;
    ROLE_DI_PUBLISHER = 3;
  }

  Role role = 1;
}

message ActionType {
  enum Action {
    ACTION_UNSPECIFIED = 0;
    ACTION_DI_VIEW = 1;
    ACTION_DI_EXPORT = 2;
    ACTION_DI_MANAGE_FILTER_OPERATOR = 3;
    ACTION_DI_MANAGE_FILTER_VALUE = 4;
    ACTION_DI_SORT_COLUMNS = 5;
    ACTION_DI_REARRANGE_COLUMNS = 6;
    ACTION_DI_ADJUST_GROUPING_BY_VALUE = 7;
    ACTION_DI_MANAGE = 8;
    ACTION_DI_PUBLISH = 9;
    ACTION_DI_CLONE = 10;
    ACTION_DI_CREATE = 11;
    ACTION_DI_UPDATE = 12;
    ACTION_DI_DELETE = 13;
    ACTION_DI_MANAGE_METRICS = 14;
    ACTION_DI_MANAGE_FILTERS = 15;
    ACTION_DI_SUBSCRIBE = 16;
  }

  Action action = 1;
}
