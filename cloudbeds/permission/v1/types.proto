syntax = "proto3";

package cloudbeds.permission.v1;

option go_package = "github.com/cloudbeds/protos-go/permission/v1;permission";
option java_package = "com.cloudbeds.permission.v1";
option java_outer_classname = "TypesProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Permission\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Permission\\V1\\GPBMetadata";

message SubjectType {
  enum Subject {
    SUBJECT_UNSPECIFIED = 0;
    SUBJECT_USER = 1;
  }

  Subject subject = 1;
}

message ScopeType {
  enum Scope {
    SCOPE_UNSPECIFIED = 0;
    SCOPE_SYSTEM = 1;
    SCOPE_ORGANIZATION = 2;
    SCOPE_PROPERTY = 3;
  }

  Scope scope = 1;
}

message RoleType {
  enum Role {
    ROLE_UNSPECIFIED = 0;
    ROLE_SUPER_ADMIN = 1;
    ROLE_ORGANIZATION_ADMIN = 2;
    ROLE_ORGANIZATION_OWNER = 3;
    ROLE_PROPERTY_OWNER = 4;
    ROLE_PROPERTY_ADMIN = 5;

    // Intentional gap for future MFD roles

    // Data Insights (DI) roles
    ROLE_DI_PUBLISHER = 300;
    ROLE_DI_AUTHOR = 301;
    ROLE_DI_VIEWER = 302;

    // Whistle roles
    ROLE_WHISTLE_SUPER_ADMIN = 400;
    ROLE_WHISTLE_ADMIN = 401;
    ROLE_WHISTLE_BASIC_USER = 402;
    ROLE_WHISTLE_BILLING_ACCESS_ONLY = 403;
  }

  Role role = 1;
}
