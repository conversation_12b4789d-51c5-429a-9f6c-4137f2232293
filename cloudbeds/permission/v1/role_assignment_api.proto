syntax = "proto3";

package cloudbeds.permission.v1;

import "cloudbeds/permission/v1/types.proto";

option go_package = "github.com/cloudbeds/protos-go/permission/v1;permission";
option java_package = "com.cloudbeds.permission.v1";
option java_outer_classname = "RoleAssignmentApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Permission\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Permission\\V1\\GPBMetadata";

service RoleAssignmentService {
  rpc AssignRole (AssignRoleRequest) returns (AssignRoleResponse);
  rpc CheckRole (CheckRoleRequest) returns (CheckRoleResponse);
  rpc RevokeRole (RevokeRoleRequest) returns (RevokeRoleResponse);
}

message AssignRoleRequest {
  SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  RoleType.Role role = 5;
}

message AssignRoleResponse {
  bool status = 1;
}

message RevokeRoleRequest {
  SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  RoleType.Role role = 5;
}

message RevokeRoleResponse {
  bool status = 1;
}

message CheckRoleRequest {
  SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  RoleType.Role role = 5;
}

message CheckRoleResponse {
  bool status = 1;
}
