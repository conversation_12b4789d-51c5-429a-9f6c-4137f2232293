syntax = "proto3";

package cloudbeds.permission.v1;

import "cloudbeds/permission/v1/types.proto";

option go_package = "github.com/cloudbeds/protos-go/permission/v1;permission";
option java_package = "com.cloudbeds.permission.v1";
option java_outer_classname = "PermissionApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Permission\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Permission\\V1\\GPBMetadata";

service PermissionService {
  rpc AssignPermission (AssignPermissionRequest) returns (AssignPermissionResponse);
  rpc RevokePermission (RevokePermissionRequest) returns (RevokePermissionResponse);
  rpc ListPermission (ListPermissionRequest) returns (ListPermissionResponse);
  rpc CheckPermission (CheckPermissionRequest) returns (CheckPermissionResponse);
}

message AssignPermissionRequest {
  SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  repeated string permissions = 5;
}

message AssignPermissionResponse {
  bool status = 1;
}

message RevokePermissionRequest {
  SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  repeated string permissions = 5;
}

message RevokePermissionResponse {
  bool status = 1;
}

message CheckPermissionRequest {
  SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  repeated string permissions = 5;
}

message CheckPermissionResponse {
  bool status = 1;
}

message ListPermissionRequest {
  SubjectType.Subject subject_type = 1;
  int64 subject_id = 2;
  ScopeType.Scope scope_type = 3;
  int64 scope_id = 4;
  repeated string permissions = 5;
}

message ListPermissionResponse {
  repeated string permissions = 1;
}