syntax = "proto3";

package cloudbeds.distributedid.sidecar.v1;

option go_package = "github.com/cloudbeds/protos-go/distributedid/sidecar/v1;sidecar";
option java_package = "com.cloudbeds.distributedid.sidecar.v1";
option java_outer_classname = "SidecarProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\DistributedId\\Sidecar\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\DistributedId\\Sidecar\\V1\\GPBMetadata";

message GetIdRequest {
}

message GetIdResponse {
  uint64 id = 1;
}

service DistributedIdSidecarService {
  rpc GetId (GetIdRequest) returns (GetIdResponse);
}