syntax = "proto3";

package cloudbeds.distributedid.server.v1;

option go_package = "github.com/cloudbeds/protos-go/distributedid/server/v1;server";
option java_package = "com.cloudbeds.distributedid.server.v1";
option java_outer_classname = "ServerProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\DistributedId\\Server\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\DistributedId\\Server\\V1\\GPBMetadata";

// Request message for generating batches of IDs.
message BatchCreateIdsRequest {
  // Required. The name / identifier of the service requesting IDs.
  string service_id = 1;
  
  // Required. The number of IDs to generate.
  uint32 batch_size = 2;
}

// Response message for id generation.
message BatchCreateIdsResponse {
  repeated uint64 ids = 1;
}

// Service to generate distributed / globally unique identifiers.
service DistributedIdService {
  // Create a batch of ids.
  rpc BatchCreateIds(BatchCreateIdsRequest) returns (BatchCreateIdsResponse);
}
