syntax = "proto3";

package cloudbeds.dispute.v1;

import "cloudbeds/dispute/v1/type.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/dispute/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "DisputeEventProto";
option java_package = "com.cloudbeds.dispute.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Dispute\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Dispute\\V1";

enum DisputeEventType {
  DISPUTE_EVENT_TYPE_UNSPECIFIED = 0;
  DISPUTE_EVENT_TYPE_CREATED = 1;
  DISPUTE_EVENT_TYPE_UPDATED = 2;
  DISPUTE_EVENT_TYPE_EVIDENCE_SUBMITTED = 3;
  DISPUTE_EVENT_TYPE_WON = 4;
  DISPUTE_EVENT_TYPE_LOST = 5;
  DISPUTE_EVENT_TYPE_RESOLVED = 6;
}

message DisputeCreatedEvent {
  Dispute dispute = 1;
}

message DisputeUpdatedEvent {
  Dispute dispute = 1;
}

message DisputeEvidenceSubmittedEvent {
  Dispute dispute = 1;
}

message DisputeWonEvent {
  Dispute dispute = 1;
}

message DisputeLostEvent {
  Dispute dispute = 1;
}

message DisputeResolvedEvent {
  Dispute dispute = 1;
}

message DisputeEvent {
  uint64 id = 1;
  string dispute_id = 2;
  DisputeEventType type = 3;
  uint64 property_id = 4;
  string event_source = 5;
  google.protobuf.Timestamp event_timestamp = 6;
  oneof payload {
    DisputeCreatedEvent dispute_created = 7;
    DisputeUpdatedEvent dispute_updated = 8;
    DisputeEvidenceSubmittedEvent dispute_evidence_submitted = 9;
    DisputeWonEvent dispute_won = 10;
    DisputeLostEvent dispute_lost = 11;
    DisputeResolvedEvent dispute_resolved = 12;
  }
}

message DisputeEventKey {
  string id = 1; //dispute ID
}
