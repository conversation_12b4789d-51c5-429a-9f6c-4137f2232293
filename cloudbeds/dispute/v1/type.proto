syntax = "proto3";

package cloudbeds.dispute.v1;

import "cloudbeds/type/v1/inventory_object.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/cloudbeds/protos-go/dispute/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "DisputeProto";
option java_package = "com.cloudbeds.dispute.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Dispute\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Dispute\\V1";

enum DisputeStatus {
  DISPUTE_STATUS_UNSPECIFIED = 0;
  DISPUTE_STATUS_IN_REVIEW = 1;
  DISPUTE_STATUS_OPEN = 2;
  DISPUTE_STATUS_LOST = 3;
  DISPUTE_STATUS_WON = 4;
  DISPUTE_STATUS_RESOLVED = 5;
}

enum DisputeSource {
  DISPUTE_SOURCE_UNSPECIFIED = 0;
  DISPUTE_SOURCE_STRIPE = 1;
}

message Dispute {
  string id = 1;
  uint64 property_id = 2;
  uint64 payment_id = 3;
  string transaction_id = 4;
  string account_id = 5;
  DisputeStatus status = 6;
  string reason = 7;
  cloudbeds.type.v1.InventoryObject inventory_object = 8;
  DisputeSource source = 9;
  google.type.Money amount = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp evidence_due_by = 12;
  google.protobuf.Timestamp evidence_submitted_at = 13;
  google.protobuf.Timestamp closed_at = 14;
}
