syntax = "proto3";

package cloudbeds.boilerplate.v1;

option go_package = "github.com/cloudbeds/protos-go/boilerplate/v1;cloudbeds";
option java_multiple_files = true;
option java_outer_classname = "BoilerplateProto";
option java_package = "com.cloudbeds.boilerplate.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Boilerplate\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Boilerplate\\V1";

service BoilerplateService {
  rpc Say(SayRequest) returns (SayResponse) {}
}

message SayRequest {
  string message = 1;
}

message SayResponse {
  string message = 1;
}
