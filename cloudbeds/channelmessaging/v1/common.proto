syntax = "proto3";

package cloudbeds.channelmessaging.v1;

option go_package = "github.com/cloudbeds/protos-go/channelmessaging/v1;channelmessaging";
option java_package = "com.cloudbeds.channelmessaging.v1";
option java_outer_classname = "CommonProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\ChannelMessaging\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\ChannelMessaging\\V1\\GPBMetadata";

message Property {
  string ma_id = 1;       // MA property id
  string mfd_id = 2;      // MFD property id
}

message Error {
  string message = 1;
  string type = 2;
  string code = 3;
}
