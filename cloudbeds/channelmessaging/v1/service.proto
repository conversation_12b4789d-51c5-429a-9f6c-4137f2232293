syntax = "proto3";

package cloudbeds.channelmessaging.v1;

import "cloudbeds/channelmessaging/v1/channel_messaging.proto";
import "cloudbeds/channelmessaging/v1/channel_status.proto";
import "cloudbeds/rpc/v1/error_details.proto";
import "google/rpc/error_details.proto";

option go_package = "github.com/cloudbeds/protos-go/channelmessaging/v1;channelmessaging";
option java_package = "com.cloudbeds.channelmessaging.v1";
option java_outer_classname = "MessagingProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\ChannelMessaging\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\ChannelMessaging\\V1\\GPBMetadata";

service MessagingService {
  rpc ReceiveMessage (ReceiveMessageRequest) returns (ReceiveMessageResponse); // Takes a message from a channel
  rpc SendMessage (SendMessageRequest) returns (SendMessageResponse);          // Sends a message to a channel
  rpc UpdateStatus (UpdateStatusRequest) returns (UpdateStatusResponse); // Updates a message delivery status
}

service ConnectivityService {
  rpc ChannelStatus (ChannelStatusRequest) returns (ChannelStatusResponse);
}

message ReceiveMessageRequest {
  Message message = 1;
}

message ReceiveMessageResponse {
  bool success = 1;
  string cb_message_id = 2;    // Message id assigned by the messaging-service
}

message SendMessageRequest {
  Message message = 1;
}

message SendMessageResponse {
  string message_id = 1;        // Message id assigned by the messaging-service
  bool pending = 2;             // If true, the message was handed over to the channel but don't mark it as delivered yet
}

message ChannelStatusRequest {
  Status request = 1;
}

message ChannelStatusResponse {
  Status response = 1;
}

enum MessageStatus {
  MESSAGE_STATUS_UNSPECIFIED = 0;
  MESSAGE_STATUS_DELIVERED = 1;       // The message was successfully delivered
  MESSAGE_STATUS_DELIVERY_FAILED = 2; // Message delivery failed
}

message UpdateStatusRequest {
  string message_id = 1; // CB message id
  MessageStatus status = 2;
  google.rpc.ErrorInfo error = 3 [deprecated = true]; // Deprecated, do not use
  cloudbeds.rpc.v1.ErrorInfo error_info = 4; // For events of type MESSAGE_STATUS_DELIVERY_FAILED
  string ma_property_id = 5; // MA property id
  string channel_id = 6; // Channel id
  string channel_message_id = 7; // Channel message id
}

message UpdateStatusResponse {
}
