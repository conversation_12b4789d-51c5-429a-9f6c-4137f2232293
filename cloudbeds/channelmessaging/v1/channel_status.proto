/*
  MessageEvent provides a message schema for Cloudbeds Messaging, and allows
  a client application to publish or consume a MessageEvent to MyAllocator messaging microservice.
  This allows a client application asynchronously send & receive chat messages for OTAs.

  The channel status component allows publishers to query property-channel connection status. First
  to see if the connection is abled and second to see if the property is able to consume and send
  messages.
*/

syntax = "proto3";

package cloudbeds.channelmessaging.v1;

import "cloudbeds/channelmessaging/v1/common.proto";

option go_package = "github.com/cloudbeds/protos-go/channelmessaging/v1;channelmessaging";
option java_package = "com.cloudbeds.channelmessaging.v1";
option java_outer_classname = "ChannelStatusProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\ChannelMessaging\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\ChannelMessaging\\V1\\GPBMetadata";

message Enabled {
  bool channel = 1;
  bool messaging = 2;
  string scoping_endpoint = 3; // the URL a property can use to "turn on" messaging
}

message Status {
  string channel_id = 1; // both request and response
  Property property = 2; // both request and response
  Enabled status = 3; // only response
  Error errors = 4; // only response
}
