/*
  MessageEvent provides a message schema for Cloudbeds Messaging, and allows
  a client application to publish or consume a MessageEvent to MyAllocator messaging microservice.
  This allows a client application asynchronously send & receive chat messages for OTAs.
*/

syntax = "proto3";

package cloudbeds.channelmessaging.v1;

import "cloudbeds/channelmessaging/v1/common.proto";
import "cloudbeds/rpc/error_details.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option go_package = "github.com/cloudbeds/protos-go/channelmessaging/v1;channelmessaging";
option java_package = "com.cloudbeds.channelmessaging.v1";
option java_outer_classname = "ChannelMessagingProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\ChannelMessaging\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\ChannelMessaging\\V1\\GPBMetadata";

enum EventType {
  EVENT_TYPE_MESSAGE_UNSPECIFIED = 0;
  EVENT_TYPE_MESSAGE_CREATED = 1;         // New message created, pending to be delivered
  EVENT_TYPE_MESSAGE_DELIVERED = 2;       // Message successfully delivered to the channel
  EVENT_TYPE_MESSAGE_RECEIVED = 3;        // Message received from a channel
  EVENT_TYPE_MESSAGE_DELIVERY_FAILED = 4; // Delivery message failed, see error
}

message MessageEvent {
  EventType type = 1;                 // Event type
  string id = 2;                      // Event id
  string source = 3;                  // Event source (whistle, myallocator...)
  string timestamp = 4;               // Event creation timestamp
  Message message = 5;                // The message
  cloudbeds.rpc.ErrorInfo error = 6 [deprecated = true]; // Deprecated, do not use
  google.rpc.Status status = 7;       // For events of type EVENT_TYPE_MESSAGE_DELIVERY_FAILED
}

message Message {
  string id = 1;                        // cb message id, empty for inbound
  string text = 2;                      // Text content of the message
  Property property = 3;                // Property or listing this message is to/from
  ChannelInfo channel_info = 4;         // Channel information
  Context context = 5;                  // Message context, reservation, inquiry...
  Sender sender = 6;                    // Message sender information, empty for outbound
  Error error = 7 [deprecated = true];  // Deprecated, do not use
  repeated Attachment attachments = 8;  // Message attachments, like images
  google.protobuf.Timestamp created_ts = 9; // For inbound, this is when the message was received/downloaded. For outbound, this is when the message was created on our side.
}

message ChannelInfo {
  string channel_id = 1;                // MA channel id: air2, boo...
  string message_id = 2;                // Channel message id, empty for outbound
  string partner_id = 3;                // Channel property id
  string conversation_id = 4;           // May be empty for outbound if reservation id is present
  string sender_id = 5;                 // Channel sender id
  string created_ts = 6;                // Channel created_at timestamp
}

enum ContextType {                  // Conversation context
  CONTEXT_TYPE_UNSPECIFIED = 0;
  CONTEXT_TYPE_RESERVATION = 1;         // Reservation
  CONTEXT_TYPE_INQUIRY = 2;             // Inquiry
  CONTEXT_TYPE_SPECIAL_OFFER = 3;       // Special offer
}

message Context {             // Conversation context information, most fields are optional
  ContextType type = 1;
  string id = 2;                          // Context id (reservation id for RESERVATION type)
  string status = 3;                      // Context status, like confirmed, cancelled, completed...
  string listing_name = 4;                // Listing (property) name
  string check_in = 5;                    // Check-in date
  string check_out = 6;                   // Check-out date
  int32 number_of_guests = 7;             // Number of guests
  int32 number_of_adults = 8;             // Number of adults
  int32 number_of_children = 9;           // Number of children
  int32 number_of_infants = 10;           // Number of infants
  int32 number_of_pets = 11;              // Number of pets
}

enum ParticipantType {
  PARTICIPANT_TYPE_UNSPECIFIED = 0;
  PARTICIPANT_TYPE_GUEST = 1;             // The guest
  PARTICIPANT_TYPE_OWNER = 2;             // The owner
  PARTICIPANT_TYPE_COHOST = 3;            // A co-host
}

message Sender {
  string name = 1;                        // Name
  string preferred_locale = 2;            // Sender preferred language
  ParticipantType participant_type = 3;   // Guest, owner, co-host...
}

message Attachment {
  reserved 2;
  string id = 1;                            // Id of the attachment on the image-service
}
