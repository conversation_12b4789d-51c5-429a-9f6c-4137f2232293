syntax = "proto3";

import "cloudbeds/currency/v1/property_currency.proto";

package cloudbeds.currency.v1;

option go_package = "github.com/cloudbeds/protos-go/currency/v1;currency";
option java_package = "com.cloudbeds.currency.v1";
option java_outer_classname = "PropertyCurrencyServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Currency\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Currency\\V1\\GPBMetadata";

message ListPropertyCurrenciesByPropertyIdRequest {
    uint64 property_id = 1;
}

message ListPropertyCurrenciesByPropertyIdResponse {
    repeated PropertyCurrency property_currency = 1;
}

message ListPropertyActiveCurrencyRatesByPropertyIdRequest {
    uint64 property_id = 1;
}

message ListPropertyActiveCurrencyRatesByPropertyIdResponse {
    repeated PropertyActiveCurrencyRate property_currency_rate = 1;
}

message GetPropertyCurrencyFormatRequest {
    uint64 property_id = 1;
}

message GetPropertyCurrencyFormatResponse {
    PropertyCurrencyFormat currency_format = 1;
}

service PropertyCurrencyService {
    rpc ListPropertyCurrenciesByPropertyId(ListPropertyCurrenciesByPropertyIdRequest) returns (ListPropertyCurrenciesByPropertyIdResponse);
    rpc ListPropertyActiveCurrencyRatesByPropertyId(ListPropertyActiveCurrencyRatesByPropertyIdRequest) returns (ListPropertyActiveCurrencyRatesByPropertyIdResponse);
    rpc GetPropertyCurrencyFormat(GetPropertyCurrencyFormatRequest) returns (GetPropertyCurrencyFormatResponse);
}