syntax = "proto3";

package cloudbeds.currency.v1;

option go_package = "github.com/cloudbeds/protos-go/currency/v1;currency";
option java_package = "com.cloudbeds.currency.v1";
option java_outer_classname = "PropertyCurrencyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Currency\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Currency\\V1\\GPBMetadata";


enum CurrencyRateType {
  CURRENCY_RATE_TYPE_UNSPECIFIED = 0;
  CURRENCY_RATE_TYPE_MANUAL = 1;
  CURRENCY_RATE_TYPE_AUTO = 2;
}

message PropertyCurrency {
  int64 id = 1;
  string currency_code = 2;
  bool display_invoice_rate = 3;
  bool display_invoice_total = 4;
  bool is_active = 5;
  int64 property_id = 6;
  CurrencyRateType rate_type = 7;
}

message PropertyActiveCurrencyRate {
  string currency_code = 1;
  double rate = 2;
}

message PropertyCurrencyFormat {
  string currency_code = 1;
  string decimal_separator = 2;
  string thousand_separator = 3;
}