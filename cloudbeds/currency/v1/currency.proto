syntax = "proto3";

package cloudbeds.currency.v1;

option go_package = "github.com/cloudbeds/protos-go/currency/v1;currency";
option java_package = "com.cloudbeds.currency.v1";
option java_outer_classname = "CurrencyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Currency\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Currency\\V1\\GPBMetadata";

service CurrencyService {
  rpc ListCurrencies (ListCurrenciesRequest) returns (ListCurrenciesResponse);
}

message ListCurrenciesRequest {
  uint32 limit = 1;
  uint32 offset = 2;
}

message ListCurrenciesResponse {
  repeated Currency currencies = 1;
}

// Metadata for a single currency
message Currency {
  // ISO 4217 code, e.g., "USD", "EUR"
  string iso_code = 1;

  // Currency name, e.g., "United States Dollar"
  string name = 2;

  // Number of fractional digits (scale), e.g., 2 for cents, 0 for JPY
  int32 scale = 3;

    // Currency symbol, e.g., "$", "€"
  string symbol = 4;
}
