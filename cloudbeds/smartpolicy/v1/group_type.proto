syntax = "proto3";

package cloudbeds.smartpolicy.v1;

option go_package = "github.com/cloudbeds/protos-go/smartpolicy/v1;smartpolicy";
option java_package = "com.cloudbeds.smartpolicy.v1";
option java_outer_classname = "GroupTypeProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\SmartPolicy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\SmartPolicy\\V1\\GPBMetadata";

import "cloudbeds/policy/v1/policy_type.proto";

// Service for managing group types.
//
// All rpcs expect a `X-PROPERTY-ID` header to be present in the request to specify which
// property to manage.
service GroupTypeService {
    // List all group types managed by a given property ID (required in X-PROPERTY-ID header).
    rpc ListGroupTypes(ListGroupTypesRequest) returns (ListGroupTypesResponse);

    // Gets a single group type.
    rpc GetGroupType(GetGroupTypeRequest) returns (GetGroupTypeResponse);
}

// Represents a "smart policy" group type
message GroupType {
    // The ID of this group type.
    int64 id = 1;

    // The name of the group type.
    string name = 2;

    // The list of policy_types assignable to this group this.
    repeated cloudbeds.policy.v1.PolicyType policy_types = 3;
}

// Request to list group types.
message ListGroupTypesRequest {}

// Reponse for listing group types.
message ListGroupTypesResponse {
    // The list of groups found.
    repeated GroupType group_type = 1;
}

// Request for getting a single group type.
message GetGroupTypeRequest {
    // The name of the group type to get.
    string name = 1;
}

// Reponse for getting a single group type.
message GetGroupTypeResponse {
    // The group type found.
    GroupType group_type = 1;
}


