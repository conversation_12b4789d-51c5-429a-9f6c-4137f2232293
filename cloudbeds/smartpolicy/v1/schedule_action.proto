
syntax = "proto3";

package cloudbeds.smartpolicy.v1;

option go_package = "github.com/cloudbeds/protos-go/smartpolicy/v1;smartpolicy";
option java_package = "com.cloudbeds.smartpolicy.v1";
option java_outer_classname = "SchedulerActionProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\SmartPolicy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\SmartPolicy\\V1\\GPBMetadata";


import "google/protobuf/timestamp.proto";
import "cloudbeds/policy/v1/rule.proto";

service ScheduleActionService {
    // List schedule actions for prepayment
    rpc ListScheduleActions(ListScheduleActionsRequest) returns (ListScheduleActionsResponse);
}

message ListScheduleActionsRequest {
    // The group ID.
    uint64 group_id = 1;

    // Reservation date time
    google.protobuf.Timestamp reservation_date_time = 2;

    // CheckIn date time
    google.protobuf.Timestamp checkin_date_time = 3;
}

message ListScheduleActionsResponse {
    // Schedule actions
    repeated ScheduldeAction schedule_actions = 1;
}

message ScheduldeAction {
    // Date need to execute the action
    google.protobuf.Timestamp date = 1;

    // rule Action to be performed
    repeated cloudbeds.policy.v1.RuleAction rule_actions = 2;

    // the order of the action
    uint32 order = 3;
}