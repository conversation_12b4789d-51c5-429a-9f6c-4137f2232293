syntax = "proto3";

package cloudbeds.smartpolicy.v1;

option go_package = "github.com/cloudbeds/protos-go/smartpolicy/v1;smartpolicy";
option java_package = "com.cloudbeds.smartpolicy.v1";
option java_outer_classname = "GroupProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\SmartPolicy\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\SmartPolicy\\V1\\GPBMetadata";

import "cloudbeds/policy/v1/policy.proto";
import "cloudbeds/smartpolicy/v1/group_type.proto";

// Service to manage groups or "smart policies".
//
// All rpcs expect a `X-PROPERTY-ID` header to be present in the request to specify which
// property to manage.
service GroupService {
    // Retrieve a single group by ID.
    rpc GetGroup(GetGroupRequest) returns (GetGroupResponse);

    // Retrieve the current group by Origin ID
    rpc GetCurrentGroupById(GetCurrentGroupByIdRequest) returns (GetCurrentGroupByIdResponse);

    // List multiple groups.
    rpc ListGroups(ListGroupsRequest) returns (ListGroupsResponse);

    // Create a new group.
    rpc CreateGroup(CreateGroupRequest) returns (CreateGroupResponse);

    // Update a group. As groups are immutable, this action is a "soft delete" + create so that
    // we maintain an audit trail.
    rpc UpdateGroup(UpdateGroupRequest) returns (UpdateGroupResponse);

    // (Soft) Delete a group.
    rpc DeleteGroup(DeleteGroupRequest) returns (DeleteGroupResponse);

    // Get the current (most recent) group for a given root ID
    rpc GetCurrentGroupByRootId(GetCurrentGroupByRootIdRequest) returns (GetCurrentGroupByRootIdResponse);

    // Get the root ID for a given group ID
    rpc GetRootIdForCurrentGroup(GetRootIdForCurrentGroupRequest) returns (GetRootIdForCurrentGroupResponse);
}

// Represents a policy group or "smart policy".
message Group {
    // The group ID.
    uint64 id = 1;

    // Required. Group name.
    string name = 2;

    // The type of group.
    GroupType group_type = 3;

    // If true, this is the default group for the property it belongs to.
    bool default_group = 4;

    // List of policies.
    repeated cloudbeds.policy.v1.Policy policies = 5;

    // ID of the root group in this group's history chain
    uint64 root_id = 6;

    // Optional. Additional policy text in multiple languages (language code -> HTML content)
    map<string, string> additional_policy_text = 7;
}

// Request message to create a new group.
// NOTE: This request requires an x-property-id header to be set.
message CreateGroupRequest {
    // Required. The name of the group.
    string name = 1;

    // Required. The name of the group type.
    string group_type_name = 2;

    // Optional. Make this the default group for this property.
    bool default_group = 3;

    // Required. Policies to assign to this group.
    repeated cloudbeds.policy.v1.CreatePolicyRequest policies = 4;

    // Optional. Additional policy text in multiple languages (language code -> HTML content)
    map<string, string> additional_policy_text = 5;
}

// Response message for group creation.
message CreateGroupResponse {
    // The newly created group.
    Group group = 1;
}

// Request message to update an existing group. This is effectively a PUT operation, so make sure to
// pass values for all fields.
// NOTE: This request requires an x-property-id header to be set.
message UpdateGroupRequest {
    // Required. ID of the group to update.
    uint64 id = 1;

    // The name of the group.
    string name = 2;

    // The name of the group type.
    string group_type_name = 3;

    // If true, make this the new default group.
    bool default_group = 4;

    // The new list of policies to assign.
    repeated cloudbeds.policy.v1.CreatePolicyRequest policies = 5;

    // Optional. Additional policy text in multiple languages (language code -> HTML content)
    map<string, string> additional_policy_text = 6;
}

// Response message for group update.
message UpdateGroupResponse {
    // The newly created group post update.
    Group group = 1;
}

// Request to list groups.
message ListGroupsRequest {
    // Optional. Search for groups by the given name. Exact match.
    optional string name = 1;
}

// Response message for listing groups.
message ListGroupsResponse {
    // The list of groups matching the ListGroupsRequest.
    repeated Group groups = 1;
}

// Request to get a single group.
message GetGroupRequest {
    // Required. The id of the group to fetch.
    uint64 id = 1;
}

// Response to get a single group.
message GetGroupResponse {
    // The group for this ID.
    Group group = 1;
}

// Request to get current group by ID
message GetCurrentGroupByIdRequest {
    // Required. The id of the crrent group to fetch.
    uint64 id = 1;
}

// Response to get current group by ID
message GetCurrentGroupByIdResponse {
    // The current group for this origin ID
    Group group = 1;
}

// Request to delete a single group.
message DeleteGroupRequest {
    // Required. The id of the group to delete.
    uint64 id = 1;
}

// Empty response for successful group deletion.
message DeleteGroupResponse {}

// Request to get current group by root ID
message GetCurrentGroupByRootIdRequest {
    // Required. The root id to lookup
    uint64 root_id = 1;
}

// Response to get current group by root ID
message GetCurrentGroupByRootIdResponse {
    // The current group for this root ID chain
    Group group = 1;
}

// Request to get root ID for a group
message GetRootIdForCurrentGroupRequest {
    // Required. The group id to lookup
    uint64 group_id = 1;
}

// Response containing the root ID for a group
message GetRootIdForCurrentGroupResponse {
    // The root ID for this group's chain
    uint64 root_id = 1;
}