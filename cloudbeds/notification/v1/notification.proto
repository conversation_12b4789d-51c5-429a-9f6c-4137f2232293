syntax = "proto3";

package cloudbeds.notification.v1;

import "google/protobuf/timestamp.proto";
import "cloudbeds/type/v1/inventory_object.proto";

option go_package = "github.com/cloudbeds/protos-go/notification/v1;notification";
option java_package = "com.cloudbeds.notification.v1";
option java_outer_classname = "NotificationProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Notification\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Notification\\V1\\GPBMetadata";

service NotificationService {
  rpc CreateNotification (CreateNotificationRequest) returns (CreateNotificationResponse);
}

message CreateNotificationRequest {
  string event_source = 1;
  uint64 property_id = 2;
  NotificationType type = 3;
  NotificationSeverity severity = 4;
  NotificationMetadata metadata = 5;
  repeated uint64 user_ids = 6;
  repeated uint64 exclude_user_ids = 7;
}

message CreateNotificationResponse {
  Notification notification = 1;
}

enum NotificationEventType {
  NOTIFICATION_EVENT_TYPE_UNSPECIFIED = 0;
  NOTIFICATION_EVENT_TYPE_CREATED = 1;
}

message NotificationEvent {
  string id = 1;
  NotificationEventType type = 2;
  string event_source = 3;
  google.protobuf.Timestamp event_timestamp = 4;
  Notification payload = 5;
}

message Notification {
  string id = 1;
  uint64 property_id = 2;
  NotificationType type = 3;
  NotificationSeverity severity = 4;
  NotificationMetadata metadata = 5;
  google.protobuf.Timestamp created_at = 6;
  repeated uint64 user_ids = 7;
  repeated uint64 exclude_user_ids = 8;
}

enum NotificationType {
  NOTIFICATION_TYPE_UNSPECIFIED = 0;
  NOTIFICATION_TYPE_GUEST_MESSAGE = 1;
  NOTIFICATION_TYPE_RESERVATION_CREATED = 2;
  NOTIFICATION_TYPE_RESERVATION_CREATED_SAME_DAY = 3;
  NOTIFICATION_TYPE_RESERVATION_CANCELLED = 4;
  NOTIFICATION_TYPE_DISPUTE = 5;
  NOTIFICATION_TYPE_TICKET_CREATED = 6;
  NOTIFICATION_TYPE_TICKET_ASSIGNED = 7;
  NOTIFICATION_TYPE_TICKET_COMPLETED = 8;
  NOTIFICATION_TYPE_TICKET_CANCELED = 9;
  NOTIFICATION_TYPE_LIVE_CHAT_MESSAGE = 10;
  NOTIFICATION_TYPE_TEAM_CHAT_MESSAGE = 11;
  NOTIFICATION_TYPE_MESSAGE_ESCALATION = 12;
}

enum NotificationSeverity {
  NOTIFICATION_SEVERITY_UNSPECIFIED = 0;
  NOTIFICATION_SEVERITY_INFO = 1;
  NOTIFICATION_SEVERITY_WARNING = 2;
  NOTIFICATION_SEVERITY_ERROR = 3;
}

message NotificationMetadata {
  oneof metadata {
    NotificationGuestMessageMetadata guest_message_metadata = 8;
    NotificationReservationMetadata reservation_created_metadata = 9;
    NotificationReservationMetadata reservation_cancelled_metadata = 10;
    NotificationDisputeMetadata dispute_metadata = 11;
    NotificationTicketMetadata ticket_metadata = 12;
    NotificationLiveChatMessageMetadata live_chat_message_metadata = 13;
    NotificationTeamChatMessageMetadata team_chat_message_metadata = 14;
    NotificationMessageEscalationMetadata message_escalation_metadata = 15;
  }
}

message NotificationGuestMessageMetadata {
  string guest_id = 1;
  string gx_guest_id = 2;
  string guest_name = 3;
  string message = 4;
  string message_type  = 5;
}

message NotificationReservationMetadata {
  string reservation_id = 1;
  string guest_name = 2;
  string guest_id = 3;
  string booking_id = 4;
  string booking_source = 5;
  string booking_source_name = 6;
}

message NotificationDisputeMetadata {
  string dispute_id = 1;
  cloudbeds.type.v1.InventoryObject inventory_object = 2;
  string inventory_object_identifier = 3;
  string inventory_object_title = 4;
  string status = 5;
  string disputed_amount = 6;
  string currency = 7;
  string card_type = 8;
  string card_number = 9;
}

message NotificationTicketMetadata {
  string ticket_id = 1;
  string ticket_title = 2;
  string assignee = 3;
  string priority = 4;
  string task_id = 5;
}

message NotificationLiveChatMessageMetadata {
  string guest_name = 1;
  string guest_email = 2;
  string message = 3;
  string conversation_id = 4;
}

message NotificationTeamChatMessageMetadata {
  string user_email = 1;
  string channel_name = 2;
  string message = 3;
  string user_name = 4;
}

message NotificationMessageEscalationMetadata {
  string guest_id = 1;
  string gx_guest_id = 2;
  string guest_name = 3;
  string message = 4;
}
