syntax = "proto3";

import "google/protobuf/struct.proto";
import "cloudbeds/accounting/v1/pending_transaction.proto";
import "cloudbeds/accounting/v1/common.proto";
import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "PendingTransactionApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

service PendingTransactionService {
    rpc ListPendingTransactions (ListPendingTransactionsRequest) returns (ListPendingTransactionsResponse);
    rpc ListPendingTransactionsWithFolio (ListPendingTransactionsWithFolioRequest) returns (ListPendingTransactionsWithFolioResponse);
}

message ListPendingTransactionsRequest {
    PendingTransactionsRequest request = 1;
}

message ListPendingTransactionsWithFolioRequest {
    PendingTransactionsRequest request = 1;
}

message PendingTransactionsRequest {
    repeated int64 property_ids = 1;
    optional Filter filter = 2;
    optional string page_token = 3;
    optional int32 page_size = 4;
    repeated OrderBy order_by = 5;

    message Filter {
        oneof kind {
            CompositeFilter composite_filter = 1;
            FieldFilter field_filter = 2;
        }
    }

    message CompositeFilter {
        cloudbeds.rpc.v1.LogicalOperator operator = 1;
        repeated Filter filters = 2;
    }

    message FieldFilter {
        enum Field {
            FIELD_UNSPECIFIED = 0;
            FIELD_ID = 1; //Expecting number value
            FIELD_INTERNAL_CODE = 2; //Expecting string value with code (1000, 1000A, 2000, 3000A, ...)
            FIELD_TRANSACTION_DATETIME = 3; //Expecting ISO formatted data as string - 1970-01-01T00:00:00.000Z
            FIELD_CUSTOMER_ID = 4; //Expecting number value
            FIELD_SOURCE_ID = 5; //Expecting number value
            FIELD_SOURCE = 6; //Expecting string value with one of the following values (RESERVATION, HOUSE_ACCOUNT, GROUP_PROFILE)
            FIELD_EXTERNAL_RELATION_ID = 7; //Expecting string value
            FIELD_EXTERNAL_RELATION = 8; //Expecting string value with one of the following values (ROOM,PAYMENT,ITEM,ITEM_POS,ADDON,RESERVATION,ACCOUNTS_RECEIVABLE,ROOM_REVENUE,FEE,TAX,ADJUSTMENT)
            FIELD_ORIGIN_ID = 9; //Expecting string value
            FIELD_CREATED_AT = 10; //Expecting ISO formatted data as string - 1970-01-01T00:00:00.000Z
            FIELD_PARENT_ID = 11; //Expecting number value
            FIELD_STATUS = 12; //Expecting string value with one of the following values (POSTED, PENDING, CANCELLED, DELETED)
            FIELD_ROUTED_SOURCE_ID = 13; //Expecting number value
            FIELD_ROUTED_SOURCE = 14;//Expecting string value with one of the following values (RESERVATION, HOUSE_ACCOUNT, GROUP_PROFILE)
            FIELD_FOLIO_ID = 15;//Expecting number value
            FIELD_ROUTED_FOLIO_ID = 16;//Expecting number value
            FIELD_CUSTOM_CODE = 17;//Expecting string value
            FIELD_ACCOUNT_CATEGORY = 18; //Expecting string value with one of the following values (DEPOSITS, will be more in future)
            FIELD_CHART_OF_ACCOUNT_TYPE = 19; //Expecting string value with one of the following values (LIABILITIES, REVENUE, ASSETS, EQUITY, EXPENSES)
            FIELD_SOURCE_IDENTIFIER = 20; //Expecting a reservation identifier for SOURCE=RESERVATION or group code for SOURCE=GROUP_PROFILE
            FIELD_SERVICE_DATE = 21; //Expecting ISO formatted data as string - 1970-01-01
        }

        cloudbeds.rpc.v1.ConditionOperator operator = 1;
        Field field = 2;
        google.protobuf.Value value = 3;
        cloudbeds.type.v1.Value cb_value = 4;
    }
}

message ListPendingTransactionsResponse {
    repeated PendingTransaction transactions = 1;
    string next_page_token = 2;
}

message ListPendingTransactionsWithFolioResponse {
    repeated PendingTransactionWithFolio transactions = 1;
    string next_page_token = 2;
}
