syntax = "proto3";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "SettingsServiceApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

enum DepositConsumptionEnum {
    DEPOSIT_CONSUMPTION_ENUM_UNSPECIFIED = 0;
    DEPOSIT_CONSUMPTION_ENUM_MANUAL = 1;
    DEPOSIT_CONSUMPTION_ENUM_CHECK_IN = 2;
}

message Settings {
    DepositConsumptionEnum deposit_consumption = 1;
}

message GetSettingsRequest {
    int64 property_id = 1;
}

message GetSettingsResponse {
    Settings settings = 1;
}

service SettingsService {
    rpc GetSettings(GetSettingsRequest) returns (GetSettingsResponse) {}
}