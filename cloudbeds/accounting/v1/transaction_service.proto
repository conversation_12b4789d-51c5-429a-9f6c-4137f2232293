syntax = "proto3";

import "cloudbeds/accounting/v1/common.proto";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "TransactionApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

service TransactionService {
    rpc GetTransactionsTotalBySource(GetTransactionsTotalBySourceRequest) returns (GetTransactionsTotalBySourceResponse);
}

message GetTransactionsTotalBySourceRequest {
    int64 property_id = 1;
    int64 source_id = 2;
    Source source = 3;
    optional TransactionType transaction_type = 4;
}

message GetTransactionsTotalBySourceResponse {
    Total totals = 1;

    message Total {
        Summary balance = 1;
    }
    message Summary {
        int64 sum = 1;
    }
}

enum TransactionType {
    TRANSACTION_TYPE_UNSPECIFIED = 0;
    TRANSACTION_TYPE_ALL = 1;
    TRANSACTION_TYPE_POSTED = 2;
    TRANSACTION_TYPE_PENDING = 3;
}