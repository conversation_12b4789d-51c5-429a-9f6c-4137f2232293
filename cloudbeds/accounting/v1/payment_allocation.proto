syntax = "proto3";

package cloudbeds.accounting.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "PaymentAllocationProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

// Payment allocation message representing the allocation of a payment to an invoice
message PaymentAllocation {
  uint64 id = 1;
  uint64 receipt_payment_id = 2;
  uint64 invoice_transaction_id = 3;
  uint64 allocated_amount = 4; // Amount in cents
  string currency = 5;
  google.protobuf.Timestamp allocation_date = 6;
  optional string allocation_reference = 7;
  uint64 property_id = 8;
  uint64 created_by_user_id = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  PaymentAllocationStatus status = 12;
  optional uint64 fiscal_document_receipt_id = 13;
  optional uint64 fiscal_document_invoice_id = 14;
}

// Payment allocation event for fiscal compliance and reporting
message PaymentAllocationEvent {
  string event_id = 1;
  PaymentAllocationEventType event_type = 2;
  PaymentAllocation allocation = 3;
  optional PaymentAllocation previous_allocation = 4;
  google.protobuf.Timestamp event_timestamp = 5;
  uint64 property_id = 6;
  uint64 triggered_by_user_id = 7;
  InvoicePaymentSummary invoice_payment_summary = 8;
  optional FiscalComplianceData fiscal_compliance_data = 9;
}

// Summary of invoice payment status after allocation event
message InvoicePaymentSummary {
  uint64 invoice_transaction_id = 1;
  uint64 total_allocated_amount = 2;
  uint64 remaining_balance = 3;
  InvoicePaymentStatus payment_status = 4;
  optional google.protobuf.Timestamp last_payment_date = 5;
}

// Fiscal compliance data for government reporting
message FiscalComplianceData {
  optional string receipt_number = 1;
  optional string invoice_number = 2;
  optional string country_code = 3;
  bool requires_government_reporting = 4;
}

// Payment allocation status
enum PaymentAllocationStatus {
  PAYMENT_ALLOCATION_STATUS_UNSPECIFIED = 0;
  PAYMENT_ALLOCATION_STATUS_ACTIVE = 1;
  PAYMENT_ALLOCATION_STATUS_CANCELLED = 2;
  PAYMENT_ALLOCATION_STATUS_REVERSED = 3;
}

// Payment allocation event type
enum PaymentAllocationEventType {
  PAYMENT_ALLOCATION_EVENT_TYPE_UNSPECIFIED = 0;
  PAYMENT_ALLOCATION_EVENT_TYPE_CREATED = 1;
  PAYMENT_ALLOCATION_EVENT_TYPE_UPDATED = 2;
  PAYMENT_ALLOCATION_EVENT_TYPE_CANCELLED = 3;
  PAYMENT_ALLOCATION_EVENT_TYPE_REVERSED = 4;
}

// Invoice payment status
enum InvoicePaymentStatus {
  INVOICE_PAYMENT_STATUS_UNSPECIFIED = 0;
  INVOICE_PAYMENT_STATUS_UNPAID = 1;
  INVOICE_PAYMENT_STATUS_PARTIALLY_PAID = 2;
  INVOICE_PAYMENT_STATUS_FULLY_PAID = 3;
  INVOICE_PAYMENT_STATUS_OVERPAID = 4;
}

// Payment allocation status for payments
enum PaymentAllocationStatusEnum {
  PAYMENT_ALLOCATION_STATUS_ENUM_UNSPECIFIED = 0;
  PAYMENT_ALLOCATION_STATUS_ENUM_UNALLOCATED = 1;
  PAYMENT_ALLOCATION_STATUS_ENUM_PARTIALLY_ALLOCATED = 2;
  PAYMENT_ALLOCATION_STATUS_ENUM_FULLY_ALLOCATED = 3;
}
