syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "cloudbeds/accounting/v1/common.proto";
import "cloudbeds/accounting/v1/internal_code.proto";
import "google/type/date.proto";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "PendingTransactionProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

enum PendingTransactionStatus {
    PENDING_TRANSACTION_STATUS_UNSPECIFIED = 0;
    PENDING_TRANSACTION_STATUS_POSTED = 1;
    PENDING_TRANSACTION_STATUS_PENDING = 2;
    PENDING_TRANSACTION_STATUS_CANCELLED = 3;
    PENDING_TRANSACTION_STATUS_DELETED = 4;
}


message PendingTransaction {
    int64 id = 1;
    int64 property_id = 2;
    // The timestamp when the transaction event was created (UTC)
    google.protobuf.Timestamp source_datetime = 3;
    // The timestamp when the transaction should actually occur at UTC
    google.protobuf.Timestamp transaction_datetime = 4;
    // The timestamp when the transaction should actually occur at property time
    google.protobuf.Timestamp transaction_datetime_property_time = 5;
    // Cloudbeds defined codes related to transaction type
    InternalCode internal_code = 6;
    // Amount of the transaction stored in the smallest unit represented by the Currency code.
    int64 amount = 7;
    // ISO Code for Currency
    string currency = 8;
    // ID of the Customer. Can be Null when there is a purchase on the hotel but is not a guest of the hotel
    int64 customer_id = 9;
    // ID to reference the parent transaction.Example - For tax on top of rate, parent_id will point to rate transaction
    optional int64 parent_id = 10;
    // ID to reference the source of the transaction (Reservation ID, House Account ID ...)
    int64 source_id = 11;
    // Type of the source
    Source source = 12;
    // ID to reference one more level of depth inside the source. For Reservation will be the Booking Room ID
    optional int64 sub_source_id = 13;
    // This field is specifically for other Squads to use it. The main idea is to be able to link it to some specific ids that can track to a transaction
    string external_relation_id = 14;
    // Type of external_relation_id
    ExternalRelation external_relation = 15;
    // This field will be used to indicate the id related to the transaction that originate the routing.
    optional int64 routed_from = 16;
    // This field will be the relationship of how the transaction and the amount are created. Example - If internal code is for Tax on this column it will be id of that tax
    optional string origin_id = 17;
    // ID to reference to the root transaction. The first transaction created on a source_id that later on can have adjustments on it
    int64 root_id = 18;
    int32 quantity = 19;
    string description = 20;
    // User Id who created the transaction.
    int64 user_id = 21;
    // Status of transaction
    PendingTransactionStatus status = 22;
    // ID to reference the source of the transaction after it is routed (Reservation ID, House Account ID ...)
    optional int64 routed_source_id = 23;
    // Type of the routed source
    optional Source routed_source = 24;
    // Date when transaction is created (property time)
    google.type.Date service_date = 25;
    // Transaction Notes. Right now has a limitation of 3000 Chars on Code so will not be displayed more than this based on product definition
    optional string notes = 26;
    // The timestamp when the transaction event was created in the service (UTC)
    google.protobuf.Timestamp created_at = 27;
    //Transaction account
    optional Account account = 28;
    // Custom code for the transaction, managed by Property.
    optional string custom_transaction_code = 29;
    // Custom code for general ledger, managed by Property.
    optional string general_ledger_custom_code = 30;
    // Scale of the currency
    int32 currency_scale = 31;
    // group code for source=group profile or reservation identifier for source=reservation
    optional string source_identifier = 32;
    // booking room identifier for source=reservation or group_profile, null for house account
    optional string sub_source_identifier = 33;
}

message PendingTransactionWithFolio {
    PendingTransaction transaction = 1;
    optional int64 folio_id = 2;
    optional int64 routed_folio_id = 3;
}