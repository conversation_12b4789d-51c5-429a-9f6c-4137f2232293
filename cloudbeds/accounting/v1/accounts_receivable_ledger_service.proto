syntax = "proto3";

import "google/protobuf/timestamp.proto";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "AccountsReceivableLedgerApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

service AccountsReceivableLedgerService {
  rpc ListAccountsReceivableLedgers(ListAccountsReceivableLedgersRequest) returns (ListAccountsReceivableLedgersResponse);
}

enum AccountsReceivableLedgerStatus {
  ACCOUNTS_RECEIVABLE_LEDGER_STATUS_UNSPECIFIED = 0;
  ACCOUNTS_RECEIVABLE_LEDGER_STATUS_OPEN = 1;
  ACCOUNTS_RECEIVABLE_LEDGER_STATUS_CLOSED = 2;
}

message AccountsReceivableLedger {
  int64 id = 1;
  AccountsReceivableLedgerStatus status = 2;
  int64 property_id = 3;
  string name = 4;
  string description = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

message ListAccountsReceivableLedgersRequest {
  repeated int64 ids = 1;
}

message ListAccountsReceivableLedgersResponse {
  repeated AccountsReceivableLedger accounts_receivable_ledgers = 1;
}
