syntax = "proto3";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "MfdTransactionProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

message MfdPublicApiTransaction {
    string transaction_id = 1;
    string property_id = 2;
    string transaction_category = 3;
    string notes = 4;
    string property_name = 5;
    int64 amount = 6;
    string currency = 7;
    optional int64 guest_id = 8;
    optional string parent_transaction_id = 9;
    optional string reservation_id = 10;
    optional string sub_reservation_id = 11;
    optional string room_id = 12;
    optional string house_account_id = 13;
    string transaction_date_time = 14;
    string transaction_date_time_utc = 15;
    optional string transaction_modified_date_time = 16;
    optional string transaction_modified_date_time_utc = 17;
    optional string guest_check_in = 18;
    optional string guest_check_out = 19;
    optional int64 room_type_id = 20;
    optional string guest_name = 21;
    optional string description = 22;
    int32 quantity = 23;
    optional bool is_deleted = 24;
    optional string custom_transaction_code = 25;
    optional string general_ledger_custom_code = 26;
    optional string customer_id = 27;
    string source_id = 28;
    string source_kind = 29;
    optional string origin_id = 30;
    optional string account = 31;
    optional string routed_from = 32;
    optional string external_relation_id = 33;
    optional string external_relation_kind = 34;
    optional string user_id = 35;
    optional string source_datetime = 36;
    string created_at = 37;
    string service_date = 38;
    optional string transaction_type = 39;
    optional string user_name = 40;
    optional string card_type = 41;
    string internal_transaction_code = 42;
    optional bool is_voided = 43;
    optional string mfd_transaction_id = 44;
    optional int64 reservation_room_id = 45;
}

message MfdPublicApiPendingTransaction {
    string transaction_id = 1;
    string property_id = 2;
    string transaction_category = 3;
    string notes = 4;
    string property_name = 5;
    int64 amount = 6;
    string currency = 7;
    optional int64 guest_id = 8;
    optional string parent_transaction_id = 9;
    optional string reservation_id = 10;
    optional string sub_reservation_id = 11;
    optional string room_id = 12;
    optional string house_account_id = 13;
    string transaction_date_time = 14;
    string transaction_date_time_utc = 15;
    optional string transaction_modified_date_time = 16;
    optional string transaction_modified_date_time_utc = 17;
    optional string guest_check_in = 18;
    optional string guest_check_out = 19;
    optional int64 room_type_id = 20;
    optional string guest_name = 21;
    optional string description = 22;
    int32 quantity = 23;
    optional bool is_deleted = 24;
    optional string custom_transaction_code = 25;
    optional string general_ledger_custom_code = 26;
    optional string customer_id = 27;
    string source_id = 28;
    string source_kind = 29;
    optional string origin_id = 30;
    optional string account = 31;
    string status = 32;
    optional string external_relation_id = 33;
    optional string external_relation_kind = 34;
    optional string user_id = 35;
    optional string source_datetime = 36;
    string created_at = 37;
    string service_date = 38;
    optional string transaction_type = 39;
    optional string user_name = 40;
    optional string card_type = 41;
    string internal_transaction_code = 42;
    optional int64 reservation_room_id = 43;
}