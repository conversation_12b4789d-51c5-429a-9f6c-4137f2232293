syntax = "proto3";

import "cloudbeds/accounting/v1/common.proto";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "DepositTransactionApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

service DepositTransactionService {
    rpc CreateDepositTransferTransactionsBySource (CreateDepositTransferTransactionsBySourceRequest) returns (CreateDepositTransferTransactionsBySourceResponse);
}

message CreateDepositTransferTransactionsBySourceRequest {
    int64 property_id = 1;
    int64 source_id = 2; // only reservation id supported so far
    Source source = 3; // only SOURCE_RESERVATION id supported so far
}

message CreateDepositTransferTransactionsBySourceResponse {
    int64 event_id = 1;
}
