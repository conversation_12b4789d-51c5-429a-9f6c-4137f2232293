syntax = "proto3";

import "cloudbeds/accounting/v1/mfd_public_api_transaction.proto";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "MfdPublicApiTransactionApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

service MfdPublicApiTransactionService {
    rpc ListMfdPublicApiTransactions(ListMfdPublicApiTransactionsRequest) returns (ListMfdPublicApiTransactionsResponse);
    rpc ListMfdPublicApiPendingTransactions(ListMfdPublicApiPendingTransactionsRequest) returns (ListMfdPublicApiPendingTransactionsResponse);
}

message ListMfdPublicApiTransactionsRequest {
    repeated int64 property_ids = 1;
    optional bool include_debit = 2;
    optional bool include_credit = 3;
    optional bool include_deleted = 4;
    optional bool include_children = 5;
    optional string reservation_id = 6;
    optional string sub_reservation_id = 7;
    optional string room_id = 8;
    optional int64 guest_id = 9;
    optional int64 house_account_id = 10;
    repeated int64 transaction_ids = 11;
    optional string results_from = 12;
    optional string results_to = 13;
    optional string modified_from = 14;
    optional string modified_to = 15;
    optional string created_from = 16;
    optional string created_to = 17;
    optional string transaction_filter = 18;
    optional int32 page_number = 19;
    optional int32 page_size = 20;
    optional string sort_by = 21;
    optional string order_by = 22;
    optional int64 last_transaction_id = 23;
    optional int64 booking_id = 24;
}

message ListMfdPublicApiTransactionsResponse {
    repeated MfdPublicApiTransaction transactions = 1;
    int32 total_count = 2;
}

message ListMfdPublicApiPendingTransactionsRequest {
    repeated int64 property_ids = 1;
    optional bool include_debit = 2;
    optional bool include_credit = 3;
    optional bool include_deleted = 4;
    optional bool include_children = 5;
    optional string reservation_id = 6;
    optional string sub_reservation_id = 7;
    optional string room_id = 8;
    optional int64 guest_id = 9;
    optional int64 house_account_id = 10;
    repeated int64 transaction_ids = 11;
    optional string results_from = 12;
    optional string results_to = 13;
    optional string modified_from = 14;
    optional string modified_to = 15;
    optional string created_from = 16;
    optional string created_to = 17;
    optional string transaction_filter = 18;
    optional int32 page_number = 19;
    optional int32 page_size = 20;
    optional string sort_by = 21;
    optional string order_by = 22;
    optional int64 last_transaction_id = 23;
    optional int64 booking_id = 24;
}

message ListMfdPublicApiPendingTransactionsResponse {
    repeated MfdPublicApiPendingTransaction transactions = 1;
    int32 total_count = 2;
}
