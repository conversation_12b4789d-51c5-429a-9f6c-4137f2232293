syntax = "proto3";

package cloudbeds.accounting.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "cloudbeds/accounting/v1/payment_allocation.proto";

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "PaymentAllocationServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

// Payment Allocation Service for managing receipt-to-invoice allocations
service PaymentAllocationService {
  // Create a new payment allocation
  rpc CreatePaymentAllocation(CreatePaymentAllocationRequest) returns (CreatePaymentAllocationResponse);

  // Get payment allocations for a specific receipt payment
  rpc GetReceiptAllocations(GetReceiptAllocationsRequest) returns (GetReceiptAllocationsResponse);

  // Get payment allocations for a specific invoice
  rpc GetInvoicePayments(GetInvoicePaymentsRequest) returns (GetInvoicePaymentsResponse);

  // Remove a payment allocation
  rpc RemovePaymentAllocation(RemovePaymentAllocationRequest) returns (google.protobuf.Empty);

  // Get payment status for an invoice
  rpc GetInvoicePaymentStatus(GetInvoicePaymentStatusRequest) returns (GetInvoicePaymentStatusResponse);

  // Get allocation summary for a payment
  rpc GetPaymentAllocationSummary(GetPaymentAllocationSummaryRequest) returns (PaymentAllocationSummaryResponse);
}

// Request to create a payment allocation
message CreatePaymentAllocationRequest {
  uint64 receipt_payment_id = 1;
  uint64 invoice_transaction_id = 2;
  uint64 allocated_amount = 3; // Amount in cents
  string currency = 4;
  uint64 property_id = 5;
  uint64 created_by_user_id = 6;
  optional string allocation_reference = 7;
  optional uint64 fiscal_document_receipt_id = 8;
  optional uint64 fiscal_document_invoice_id = 9;
}

// Response for create payment allocation operations
message CreatePaymentAllocationResponse {
  uint64 allocation_id = 1;
  uint64 receipt_payment_id = 2;
  uint64 invoice_transaction_id = 3;
  uint64 allocated_amount = 4;
  string currency = 5;
  google.protobuf.Timestamp allocation_date = 6;
  optional string allocation_reference = 7;
  uint64 property_id = 8;
  PaymentAllocationStatus status = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  optional uint64 fiscal_document_receipt_id = 12;
  optional uint64 fiscal_document_invoice_id = 13;
}

// Request to get allocations for a receipt payment
message GetReceiptAllocationsRequest {
  uint64 receipt_payment_id = 1;
  uint64 property_id = 2;
}

// Response with receipt allocations
message GetReceiptAllocationsResponse {
  repeated AllocationSummary allocations = 1;
  uint64 total_allocated_amount = 2;
  uint64 remaining_unallocated_amount = 3;
}

// Request to get payments for an invoice
message GetInvoicePaymentsRequest {
  uint64 invoice_transaction_id = 1;
  uint64 property_id = 2;
}

// Response with invoice payments
message GetInvoicePaymentsResponse {
  repeated AllocationSummary payments = 1;
  uint64 total_allocated_amount = 2;
  uint64 remaining_balance = 3;
  InvoicePaymentStatus payment_status = 4;
}

// Request to remove a payment allocation
message RemovePaymentAllocationRequest {
  uint64 allocation_id = 1;
  uint64 property_id = 2;
  uint64 removed_by_user_id = 3;
}

// Request to get invoice payment status
message GetInvoicePaymentStatusRequest {
  uint64 invoice_transaction_id = 1;
  uint64 property_id = 2;
}

// Response with invoice payment status
message GetInvoicePaymentStatusResponse {
  uint64 invoice_transaction_id = 1;
  uint64 invoice_amount = 2;
  uint64 total_allocated_amount = 3;
  uint64 remaining_balance = 4;
  InvoicePaymentStatus payment_status = 5;
  optional google.protobuf.Timestamp last_payment_date = 6;
}

// Request to get payment allocation summary
message GetPaymentAllocationSummaryRequest {
  uint64 receipt_payment_id = 1;
  uint64 property_id = 2;
}

// Response with payment allocation summary
message PaymentAllocationSummaryResponse {
  uint64 receipt_payment_id = 1;
  uint64 payment_amount = 2;
  uint64 total_allocated_amount = 3;
  uint64 remaining_unallocated_amount = 4;
  PaymentAllocationStatus allocation_status = 5;
  optional google.protobuf.Timestamp last_allocation_date = 6;
  repeated AllocationSummary allocations = 7;
}

// Summary of an allocation
message AllocationSummary {
  uint64 allocation_id = 1;
  uint64 receipt_payment_id = 2;
  uint64 invoice_transaction_id = 3;
  uint64 allocated_amount = 4;
  string currency = 5;
  google.protobuf.Timestamp allocation_date = 6;
  optional string allocation_reference = 7;
  PaymentAllocationStatus status = 8;
  optional string receipt_number = 9;
  optional string invoice_number = 10;
  optional uint64 fiscal_document_receipt_id = 11;
  optional uint64 fiscal_document_invoice_id = 12;
}


