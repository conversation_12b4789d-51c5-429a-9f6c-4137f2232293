syntax = "proto3";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "TransactionToMfdTransactionApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

service TransactionToMfdTransactionService {
    rpc GetMfdTransactionsByTransactionIds(GetMfdTransactionsByTransactionIdsRequest) returns (GetMfdTransactionsByTransactionIdsResponse);
    rpc GetTransactionIdsByMfdTransactions(GetTransactionIdsByMfdTransactionsRequest) returns (GetTransactionIdsByMfdTransactionsResponse);
}

message TransactionsMap {
    string mfd_transaction_id = 1;
    uint64 transaction_id = 2;
}

message GetMfdTransactionsByTransactionIdsRequest {
    repeated uint64 transaction_ids = 1;
}

message GetTransactionIdsByMfdTransactionsRequest {
    repeated string transaction_ids = 1;
}

message GetMfdTransactionsByTransactionIdsResponse {
    repeated TransactionsMap transactions_map = 1;
}

message GetTransactionIdsByMfdTransactionsResponse {
    repeated TransactionsMap transactions_map = 1;
}
