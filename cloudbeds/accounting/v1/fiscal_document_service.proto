syntax = "proto3";

package cloudbeds.accounting.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "FiscalDocumentServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

// Fiscal Document Service for managing invoices, receipts, and pro-forma documents
service FiscalDocumentService {
  // Create a new invoice
  rpc CreateInvoice(CreateInvoiceRequest) returns (CreateInvoiceResponse);

  // Create a new receipt
  rpc CreateReceipt(CreateReceiptRequest) returns (CreateReceiptResponse);

  // Create a new pro-forma invoice
  rpc CreateProFormaInvoice(CreateProFormaInvoiceRequest) returns (CreateProFormaInvoiceResponse);

  // Get fiscal document by ID
  rpc GetFiscalDocument(GetFiscalDocumentRequest) returns (GetFiscalDocumentResponse);

  // Update fiscal document status
  rpc UpdateFiscalDocumentStatus(UpdateFiscalDocumentStatusRequest) returns (UpdateFiscalDocumentStatusResponse);

  // Get fiscal documents by source
  rpc GetFiscalDocumentsBySource(GetFiscalDocumentsBySourceRequest) returns (GetFiscalDocumentsBySourceResponse);

  // Get payment status for fiscal document
  rpc GetFiscalDocumentPaymentStatus(GetFiscalDocumentPaymentStatusRequest) returns (GetFiscalDocumentPaymentStatusResponse);
}

// Request to create an invoice
message CreateInvoiceRequest {
  uint64 property_id = 1;
  uint64 user_id = 2;
  uint64 source_id = 3;
  SourceKind source_kind = 4;
  repeated TransactionItem transactions = 5;
  Recipient recipient = 6;
  string currency = 7;
  google.protobuf.Timestamp invoice_date = 8;
  optional string external_id = 9;
  optional string notes = 10;
}

// Request to create a receipt
message CreateReceiptRequest {
  uint64 property_id = 1;
  uint64 user_id = 2;
  uint64 source_id = 3;
  SourceKind source_kind = 4;
  repeated PaymentItem payments = 5;
  Recipient recipient = 6;
  string currency = 7;
  google.protobuf.Timestamp receipt_date = 8;
  optional string external_id = 9;
  optional string notes = 10;
}

// Request to create a pro-forma invoice
message CreateProFormaInvoiceRequest {
  uint64 property_id = 1;
  uint64 user_id = 2;
  uint64 source_id = 3;
  SourceKind source_kind = 4;
  repeated TransactionItem transactions = 5;
  Recipient recipient = 6;
  string currency = 7;
  google.protobuf.Timestamp proforma_date = 8;
  optional string external_id = 9;
  optional string notes = 10;
  optional google.protobuf.Timestamp valid_until = 11;
}

// Response for fiscal document operations
message FiscalDocumentResponse {
  uint64 id = 1;
  string number = 2;
  uint64 property_id = 3;
  uint64 source_id = 4;
  uint64 user_id = 5;
  SourceKind source_kind = 6;
  FiscalDocumentKind kind = 7;
  FiscalDocumentStatus status = 8;
  google.protobuf.Timestamp document_date = 9;
  string currency = 10;
  uint64 amount = 11; // Amount in cents
  uint64 balance = 12; // Balance in cents
  optional string external_id = 13;
  optional string file_path = 14;
  optional string url = 15;
  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 17;
  Recipient recipient = 18;
  repeated TransactionItem transactions = 19;
  PaymentStatus payment_status = 20;
  optional google.protobuf.Timestamp last_payment_date = 21;
  uint64 total_allocated_amount = 22;
}

// Response for CreateInvoice RPC
message CreateInvoiceResponse {
  FiscalDocumentResponse fiscal_document = 1;
}

// Response for CreateReceipt RPC
message CreateReceiptResponse {
  FiscalDocumentResponse fiscal_document = 1;
}

// Response for CreateProFormaInvoice RPC
message CreateProFormaInvoiceResponse {
  FiscalDocumentResponse fiscal_document = 1;
}

// Response for GetFiscalDocument RPC
message GetFiscalDocumentResponse {
  FiscalDocumentResponse fiscal_document = 1;
}

// Response for UpdateFiscalDocumentStatus RPC
message UpdateFiscalDocumentStatusResponse {
  FiscalDocumentResponse fiscal_document = 1;
}

// Request to get fiscal document
message GetFiscalDocumentRequest {
  uint64 id = 1;
  uint64 property_id = 2;
}

// Request to update fiscal document status
message UpdateFiscalDocumentStatusRequest {
  uint64 id = 1;
  uint64 property_id = 2;
  FiscalDocumentStatus status = 3;
  uint64 updated_by_user_id = 4;
  optional string reason = 5;
}

// Request to get fiscal documents by source
message GetFiscalDocumentsBySourceRequest {
  uint64 source_id = 1;
  SourceKind source_kind = 2;
  uint64 property_id = 3;
  optional FiscalDocumentKind kind_filter = 4;
  optional FiscalDocumentStatus status_filter = 5;
}

// Response with multiple fiscal documents
message GetFiscalDocumentsBySourceResponse {
  repeated FiscalDocumentResponse documents = 1;
}

// Request to get payment status
message GetFiscalDocumentPaymentStatusRequest {
  uint64 id = 1;
  uint64 property_id = 2;
}

// Response with payment status
message GetFiscalDocumentPaymentStatusResponse {
  uint64 id = 1;
  PaymentStatus payment_status = 2;
  uint64 total_amount = 3;
  uint64 total_allocated_amount = 4;
  uint64 remaining_balance = 5;
  optional google.protobuf.Timestamp last_payment_date = 6;
}

// Transaction item for invoices and pro-forma
message TransactionItem {
  uint64 transaction_id = 1;
  string description = 2;
  uint64 amount = 3; // Amount in cents
  string currency = 4;
  optional string internal_code = 5;
}

// Payment item for receipts
message PaymentItem {
  uint64 payment_id = 1;
  string description = 2;
  uint64 amount = 3; // Amount in cents
  string currency = 4;
  string payment_method = 5;
  google.protobuf.Timestamp payment_date = 6;
}

// Recipient information
message Recipient {
  string name = 1;
  optional string email = 2;
  optional string address = 3;
  optional string city = 4;
  optional string country = 5;
  optional string postal_code = 6;
  optional string tax_id = 7;
  optional string phone = 8;
}

// Source kind enum
enum SourceKind {
  SOURCE_KIND_UNSPECIFIED = 0;
  SOURCE_KIND_RESERVATION = 1;
  SOURCE_KIND_HOUSE_ACCOUNT = 2;
  SOURCE_KIND_GROUP_PROFILE = 3;
  SOURCE_KIND_ACCOUNTS_RECEIVABLE_LEDGER = 4;
}

// Fiscal document kind enum
enum FiscalDocumentKind {
  FISCAL_DOCUMENT_KIND_UNSPECIFIED = 0;
  FISCAL_DOCUMENT_KIND_INVOICE = 1;
  FISCAL_DOCUMENT_KIND_CREDIT_NOTE = 2;
  FISCAL_DOCUMENT_KIND_RECEIPT = 3;
  FISCAL_DOCUMENT_KIND_RECTIFY_INVOICE = 4;
  FISCAL_DOCUMENT_KIND_PRO_FORMA_INVOICE = 5;
}

// Fiscal document status enum
enum FiscalDocumentStatus {
  FISCAL_DOCUMENT_STATUS_UNSPECIFIED = 0;
  FISCAL_DOCUMENT_STATUS_DRAFT = 1;
  FISCAL_DOCUMENT_STATUS_PENDING = 2;
  FISCAL_DOCUMENT_STATUS_COMPLETED = 3;
  FISCAL_DOCUMENT_STATUS_FAILED = 4;
  FISCAL_DOCUMENT_STATUS_CANCELLED = 5;
}

// Payment status enum
enum PaymentStatus {
  PAYMENT_STATUS_UNSPECIFIED = 0;
  PAYMENT_STATUS_UNPAID = 1;
  PAYMENT_STATUS_PARTIALLY_PAID = 2;
  PAYMENT_STATUS_FULLY_PAID = 3;
}
