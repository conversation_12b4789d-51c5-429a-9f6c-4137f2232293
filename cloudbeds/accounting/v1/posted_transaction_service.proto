syntax = "proto3";

import "google/protobuf/struct.proto";
import "cloudbeds/accounting/v1/transaction.proto";
import "cloudbeds/accounting/v1/common.proto";
import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "PostedTransactionApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

service PostedTransactionService {
    rpc ListPostedTransactions (ListPostedTransactionsRequest) returns (ListPostedTransactionsResponse);
    rpc ListPostedTransactionsWithFolio (ListPostedTransactionsWithFolioRequest) returns (ListPostedTransactionsWithFolioResponse);
}

message ListPostedTransactionsRequest {
    PostedTransactionsRequest request = 1;
}

message ListPostedTransactionsWithFolioRequest {
    PostedTransactionsRequest request = 1;
}

message PostedTransactionsRequest {
    repeated int64 property_ids = 1;
    optional Filter filter = 2;
    optional string page_token = 3;
    optional int32 page_size = 4;
    repeated OrderBy order_by = 5;
    optional bool exclude_voided = 6;

    message Filter {
        oneof kind {
            CompositeFilter composite_filter = 1;
            FieldFilter field_filter = 2;
        }
    }

    message CompositeFilter {
        cloudbeds.rpc.v1.LogicalOperator operator = 1;
        repeated Filter filters = 2;
    }


    message FieldFilter {
        enum Field {
            FIELD_UNSPECIFIED = 0;
            FIELD_ID = 1; //Expecting number value
            FIELD_INTERNAL_CODE = 2; //Expecting string value with code (1000, 1000A, 2000, 3000A, ...)
            FIELD_TRANSACTION_DATETIME = 3; //Expecting ISO formatted data as string - 1970-01-01T00:00:00.000Z
            FIELD_CUSTOMER_ID = 4; //Expecting number value
            FIELD_SOURCE_ID = 5; //Expecting number value
            FIELD_SOURCE = 6; //Expecting string value with one of the following values (RESERVATION, HOUSE_ACCOUNT, GROUP_PROFILE, ACCOUNTS_RECEIVABLE_LEDGER)
            FIELD_EXTERNAL_RELATION_ID = 7; //Expecting string value
            FIELD_EXTERNAL_RELATION = 8; //Expecting string value with one of the following values (ROOM,PAYMENT,ITEM,ITEM_POS,ADDON,RESERVATION,ACCOUNTS_RECEIVABLE,ROOM_REVENUE,FEE,TAX,ADJUSTMENT)
            FIELD_ORIGIN_ID = 9; //Expecting string value
            FIELD_ROUTED_FROM = 10; //Expecting number value
            FIELD_CREATED_AT = 11; //Expecting ISO formatted data as string - 1970-01-01T00:00:00.000Z
            FIELD_PARENT_ID = 12; //Expecting number value
            FIELD_FOLIO_ID = 13; //Expecting number value
            FIELD_CUSTOM_CODE = 14;//Expecting string value
            FIELD_ACCOUNT_CATEGORY = 15; //Expecting string value with one of the following values (DEPOSITS, will be more in future)
            FIELD_CHART_OF_ACCOUNT_TYPE = 16; //Expecting string value with one of the following values (LIABILITIES, REVENUE, ASSETS, EQUITY, EXPENSES)
            FIELD_SOURCE_IDENTIFIER = 17; //Expecting a reservation identifier for SOURCE=RESERVATION or group code for SOURCE=GROUP_PROFILE
            FIELD_TRIAL_BALANCE_ID = 18; //Expecting number value
            FIELD_SERVICE_DATE = 19; //Expecting ISO formatted data as string - 1970-01-01
            FIELD_ROOT_ID = 20; //Expecting number value
        }

        cloudbeds.rpc.v1.ConditionOperator operator = 1;
        Field field = 2;
        google.protobuf.Value value = 3;
        cloudbeds.type.v1.Value cb_value = 4;
    }
}

message ListPostedTransactionsResponse {
    repeated Transaction transactions = 1;
    string next_page_token = 2;
}

message ListPostedTransactionsWithFolioResponse {
    repeated TransactionWithFolio transactions = 1;
    string next_page_token = 2;
}
