syntax = "proto3";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "InternalCodeProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

enum InternalCodeGroup {
  INTERNAL_CODE_GROUP_UNSPECIFIED = 0;
  INTERNAL_CODE_GROUP_ROOM_REVENUE_RATE = 1;
  INTERNAL_CODE_GROUP_ROOM_REVENUE_MANUAL = 2;
  INTERNAL_CODE_GROUP_ROOM_REVENUE_CANCELLATION = 3;
  INTERNAL_CODE_GROUP_ROOM_REVENUE_NO_SHOW = 4;
  INTERNAL_CODE_GROUP_ITEM_SERVICE= 5;
  INTERNAL_CODE_GROUP_ADDON = 6;
  INTERNAL_CODE_GROUP_CUSTOM_ITEM = 7;
  INTERNAL_CODE_GROUP_FEE = 8;
  INTERNAL_CODE_GROUP_TAX = 9;
  INTERNAL_CODE_GROUP_PAYMENT = 10;
  INTERNAL_CODE_GROUP_CITY_LEDGER = 11; //Renamed to accounts receivable
  INTERNAL_CODE_GROUP_N_A = 12;
  INTERNAL_CODE_GROUP_ACCOUNTS_RECEIVABLE = 13;
}


message InternalCode {
    int64 id = 1;
    string code = 2;
    string description = 3;
    InternalCodeGroup group = 4;
}

