syntax = "proto3";

package cloudbeds.accounting.v1;

import "cloudbeds/rpc/v1/common.proto";

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "CommonProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

enum Source {
  SOURCE_UNSPECIFIED = 0;
  SOURCE_RESERVATION = 1;
  SOURCE_HOUSE_ACCOUNT = 2;
  SOURCE_GROUP_PROFILE = 3;
  SOURCE_CITY_LEDGER = 4; //Renamed to accounts receivable ledger
  SOURCE_ACCOUNTS_RECEIVABLE_LEDGER = 5;
}

message Account {
  uint64 id = 1;
  string description = 2;
  string name = 3;
  AccountCategory category = 4;
  ChartOfAccountType chart_of_account_type = 5;
}

enum AccountCategory {
  ACCOUNT_CATEGORY_UNSPECIFIED = 0;
  ACCOUNT_CATEGORY_DEPOSITS = 1;
}

enum ChartOfAccountType {
  CHART_OF_ACCOUNT_TYPE_UNSPECIFIED = 0;
  CHART_OF_ACCOUNT_TYPE_ASSETS = 1;
  CHART_OF_ACCOUNT_TYPE_LIABILITIES = 2;
  CHART_OF_ACCOUNT_TYPE_EQUITY = 3;
  CHART_OF_ACCOUNT_TYPE_REVENUE = 4;
  CHART_OF_ACCOUNT_TYPE_EXPENSES = 5;
}

enum ExternalRelation {
  EXTERNAL_RELATION_UNSPECIFIED = 0;
  EXTERNAL_RELATION_ROOM = 1;
  EXTERNAL_RELATION_PAYMENT = 2;
  EXTERNAL_RELATION_ITEM = 3;
  EXTERNAL_RELATION_ITEM_POS = 4;
  EXTERNAL_RELATION_ADDON = 5;
  EXTERNAL_RELATION_RESERVATION = 6;
  EXTERNAL_RELATION_CITY_LEDGER = 7; //Renamed to accounts receivable
  EXTERNAL_RELATION_ROOM_REVENUE = 8;
  EXTERNAL_RELATION_TAX = 9;
  EXTERNAL_RELATION_FEE = 10;
  EXTERNAL_RELATION_ADJUSTMENT = 11;
  EXTERNAL_RELATION_PAYMENT_FEE = 12;
  EXTERNAL_RELATION_ACCOUNTS_RECEIVABLE = 13;
}


message OrderBy {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_INTERNAL_CODE = 1;
    FIELD_TRANSACTION_DATETIME = 2;
    FIELD_SOURCE_ID = 3;
    FIELD_CREATED_AT = 4;
    FIELD_SERVICE_DATE = 5;
  }

  Field field = 1;
  cloudbeds.rpc.v1.Direction direction = 2;
}

enum ConditionOperator {
  CONDITION_OPERATOR_UNSPECIFIED = 0;
  CONDITION_OPERATOR_GREATER_THAN_OR_EQUAL = 1;
  CONDITION_OPERATOR_LESS_THAN_OR_EQUAL = 2;
  CONDITION_OPERATOR_GREATER_THAN = 3;
  CONDITION_OPERATOR_LESS_THAN = 4;
  CONDITION_OPERATOR_EQUAL = 5;
  CONDITION_OPERATOR_NOT_EQUAL = 6;
  CONDITION_OPERATOR_IN = 7;
  CONDITION_OPERATOR_NOT_IN = 8;
}

enum LogicalOperator {
  LOGICAL_OPERATOR_UNSPECIFIED = 0;
  LOGICAL_OPERATOR_AND = 1;
  LOGICAL_OPERATOR_OR = 2;
}
