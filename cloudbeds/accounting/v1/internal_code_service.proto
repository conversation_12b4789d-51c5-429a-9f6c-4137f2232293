syntax = "proto3";

import "cloudbeds/accounting/v1/internal_code.proto";

package cloudbeds.accounting.v1;

option go_package = "github.com/cloudbeds/protos-go/accounting/v1;accounting";
option java_package = "com.cloudbeds.accounting.v1";
option java_outer_classname = "InternalCodeApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Accounting\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Accounting\\V1\\GPBMetadata";

service InternalCodeService {
    rpc ListInternalCodes(ListInternalCodesRequest) returns (ListInternalCodesResponse);
    rpc ListInternalCodeByCode(ListInternalCodeByCodeRequest) returns (ListInternalCodeByCodeResponse);
}

message ListInternalCodesRequest {}

message ListInternalCodesResponse {
    repeated InternalCode internal_codes = 1;
}

message ListInternalCodeByCodeRequest {
    string code = 1;
}

message ListInternalCodeByCodeResponse {
    InternalCode internal_code = 1;
}