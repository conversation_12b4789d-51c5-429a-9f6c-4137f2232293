syntax = "proto3";

package cloudbeds.user.v1;

option go_package = "github.com/cloudbeds/protos-go/user/v1;user";
option java_package = "com.cloudbeds.user.v1";
option java_outer_classname = "UserApiProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\User\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\User\\V1\\GPBMetadata";

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "cloudbeds/organization/v1/common.proto";
import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";

service UserService {
  rpc ListUsers (ListUsersRequest) returns (ListUsersResponse);
  rpc GetUser (GetUserRequest) returns (GetUserResponse);
  rpc CreateUser (CreateUserRequest) returns (CreateUserResponse);
  rpc UpdateUser (UpdateUserRequest) returns (UpdateUserResponse);
  rpc DeleteUser (DeleteUserRequest) returns (DeleteUserResponse);
  rpc GetUserAssignment(GetUserAssignmentRequest) returns (GetUserAssignmentResponse);
  rpc ListUsersByAssignments(ListUsersByAssignmentsRequest) returns (ListUsersByAssignmentsResponse);
  rpc ResetPassword (ResetPasswordRequest) returns (ResetPasswordResponse);
  rpc ForgotPassword (ForgotPasswordRequest) returns (ForgotPasswordResponse);
  rpc ResetMfa (ResetMfaRequest) returns (ResetMfaResponse);
  rpc EnableMfa (EnableMfaRequest) returns (EnableMfaResponse);
}

enum UserType {
  USER_TYPE_UNSPECIFIED = 0;
  USER_TYPE_EMPLOYEE = 1;
  USER_TYPE_CUSTOMER = 2;
}

enum MFAMode {
  MFA_MODE_UNSPECIFIED = 0;
  MFA_MODE_OKTA = 1;
}

enum AuthType {
  AUTH_TYPE_UNSPECIFIED = 0;
  AUTH_TYPE_GOOGLE = 1;
  AUTH_TYPE_APPLE = 2;
  AUTH_TYPE_OKTA_MFA_LIMITED = 3;
}

message MFA {
  MFAMode mode = 1;
  bool verified = 2;
}

message User {
  uint64 id = 1;
  optional string okta_id = 2;
  string email = 3;
  optional string first_name = 4;
  optional string last_name = 5;
  bool is_active = 6;
  UserType type = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  optional google.protobuf.Timestamp deleted_at = 10;
  google.protobuf.Timestamp last_login_at = 11;
  string ip_address = 12;
  optional MFA mfa = 13;
  optional string language = 14;
  optional int32 auto_logout_minutes = 15;
  optional AuthType auth_type = 16;
}

message Filter {
  oneof kind {
    CompositeFilter composite_filter = 1;
    FieldFilter field_filter = 2;
  }
}

message CompositeFilter {
  cloudbeds.rpc.v1.LogicalOperator operator = 1;
  repeated Filter filters = 2;
}

message FieldFilter {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_ID = 1;
    FIELD_OKTA_ID = 2;
    FIELD_EMAIL = 3;
    FIELD_FIRST_NAME = 4;
    FIELD_LAST_NAME = 5;
    FIELD_IS_ACTIVE = 6;
    FIELD_TYPE = 7;
    FIELD_CREATED_AT = 8;
    FIELD_UPDATED_AT = 9;
    FIELD_DELETED_AT = 10;
    FIELD_LAST_LOGIN_AT = 11;
    FIELD_IP_ADDRESS = 12;
    FIELD_LANGUAGE = 13;
  }

  cloudbeds.rpc.v1.ConditionOperator operator = 1;
  Field field = 2;
  google.protobuf.Value value = 3;
  cloudbeds.type.v1.Value cb_value = 4;
}

message OrderBy {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_ID = 1;
    FIELD_OKTA_ID = 2;
    FIELD_EMAIL = 3;
    FIELD_FIRST_NAME = 4;
    FIELD_LAST_NAME = 5;
    FIELD_IS_ACTIVE = 6;
    FIELD_TYPE = 7;
    FIELD_CREATED_AT = 8;
    FIELD_UPDATED_AT = 9;
    FIELD_DELETED_AT = 10;
    FIELD_LAST_LOGIN_AT = 11;
    FIELD_IP_ADDRESS = 12;
    FIELD_LANGUAGE = 13;
  }

  Field field = 1;
  cloudbeds.rpc.v1.Direction direction = 2;
}

message ListUsersRequest {
  optional Filter filter = 1;
  repeated OrderBy order_by = 2;

  // Number of elements to retrieve in a single page.
  int32 page_size = 100;

  // Token of the page to retrieve. If not specified, the first
  // page of results will be returned. Use the value obtained from
  // `next_page_token` in the previous response in order to request
  // the next page of results.
  string page_token = 101;
}

message ListUsersResponse {
  repeated User users = 1;

  // Pagination token used to retrieve the next page of results.
  // Pass the content of this string as the `page_token` attribute of
  // the next request. `next_page_token` is not returned for the last
  // page.
  string next_page_token = 2;
}

message GetUserRequest {
  optional uint64 id = 1;
  optional string email = 2;
  optional string okta_id = 3;
}

message GetUserResponse {
  User user = 1;
}

message CreateUserRequest {
  string email = 1;
  string first_name = 2;
  string last_name = 3;
  bool is_active = 4;
  UserType type = 5;
  optional string island = 6;
  optional uint64 user_id = 7;
  optional string language = 8;
  optional int32 auto_logout_minutes = 9;
  optional bool enable_mfa = 10;
  optional string ip_address = 11;
  optional AuthType auth_type = 12;
  optional bool enable_okta = 13;
}

message CreateUserResponse {
  User user = 1;
}

message UpdateUserRequest {
  uint64 id = 1;
  optional string email = 2;
  optional string first_name = 3;
  optional string last_name = 4;
  optional bool is_active = 5;
  optional UserType type = 6;
  optional string language = 7;
  optional int32 auto_logout_minutes = 8;
  optional string ip_address = 9;
  optional AuthType auth_type = 10;
}

message UpdateUserResponse {
  User user = 1;
}

message DeleteUserRequest {
  uint64 id = 1;
}

message DeleteUserResponse {}

message UserProperty {
  uint64 id = 1;
  string name = 2;
  cloudbeds.organization.v1.IslandConfiguration island = 3;
}

message UserOrganization {
  uint64 id = 1;
  string name = 2;
  cloudbeds.organization.v1.IslandConfiguration island = 3;
  repeated UserProperty properties = 5;
}

// The UserAssignment message represents the organizations and properties
// that the user is assigned to.
message UserAssignment {
  // The organizations the user is assigned to.
  repeated UserOrganization organizations = 1;
  // The properties the user is assigned to, that do not belong to organizations. In the future,
  // this should all be rolled up into organizations when each property belongs to one.
  repeated UserProperty properties = 2;
}

message GetUserAssignmentRequest {
  enum AssignmentScope {
    ASSIGNMENT_SCOPE_UNSPECIFIED = 0;
    ASSIGNMENT_SCOPE_PROPERTY = 1;
    ASSIGNMENT_SCOPE_ORGANIZATION = 2;
  }

  optional uint64 id = 1;
  optional string email = 2;
  optional AssignmentScope scope = 3;
}

message GetUserAssignmentResponse {
  UserAssignment assignment = 1;
}

message ListUsersByAssignmentsScope {
  enum Scope {
    SCOPE_UNSPECIFIED = 0;
    SCOPE_PROPERTY = 1;
    SCOPE_ORGANIZATION = 2;
    SCOPE_ALL = 3;
  }
  Scope scope = 1;
}

message ListUsersByOrganizations {
  repeated uint64 organization_ids = 1;
  optional ListUsersByAssignmentsScope scope = 2;
}

message ListUsersByRoles {
  repeated string role_ids = 1;
  optional ListUsersByAssignmentsScope scope = 2;
  optional bool is_assignment_active = 3;
}

message ListUsersByProperties {
  repeated uint64 property_ids = 1;
  optional bool include_organization_users = 2;
}

message ListUsersByAssignmentsRequest {
  oneof params {
    ListUsersByOrganizations organizations = 1;
    ListUsersByProperties properties = 2;
    ListUsersByRoles roles = 3;
  }
  // Number of elements to retrieve in a single page.
  optional int32 page_size = 100;

  // Token of the page to retrieve. If not specified, the first
  // page of results will be returned. Use the value obtained from
  // `next_page_token` in the previous response in order to request
  // the next page of results.
  optional string page_token = 101;
}

message ListUsersByAssignmentsResponse {
  repeated User users = 1;

  // Pagination token used to retrieve the next page of results.
  // Pass the content of this string as the `page_token` attribute of
  // the next request. `next_page_token` is not returned for the last
  // page.
  string next_page_token = 2;
}

message ResetPasswordRequest {
  oneof identifier {
    uint64 id = 1;
    string email = 2;
  }
  optional bool send_email = 3;
}

message ResetPasswordResponse {}

message ForgotPasswordRequest {
  oneof identifier {
    uint64 id = 1;
    string email = 2;
  }
}

message ForgotPasswordResponse {}

message ResetMfaRequest {
  oneof identifier {
    uint64 id = 1;
    string email = 2;
  }
}

message ResetMfaResponse {}

message EnableMfaRequest {
  oneof identifier {
    uint64 id = 1;
    string email = 2;
  }
}

message EnableMfaResponse {}
