syntax = "proto3";

package cloudbeds.memo.v1;

import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/memo/v1;memo";
option java_multiple_files = true;
option java_outer_classname = "MemoProto";
option java_package = "com.cloudbeds.memo.v1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Memo\\V1\\GPBMetadata";
option php_namespace = "Cloudbeds\\Protos\\Memo\\V1";

service MemoService {
  rpc CreateMemo(CreateMemoRequest) returns (CreateMemoResponse) {}
  rpc GetMemo(GetMemoRequest) returns (GetMemoResponse) {}
  rpc ListMemos(ListMemosRequest) returns (ListMemosResponse) {}
  rpc UpdateMemo(UpdateMemoRequest) returns (UpdateMemoResponse) {}
  rpc DeleteMemo(DeleteMemoRequest) returns (DeleteMemoResponse) {}
}

message Memo {
  int64 id = 1;
  int64 organization_id = 2;
  repeated int64 property_ids = 3;
  string content = 4;
  google.protobuf.Timestamp publish_at = 5;
  google.protobuf.Timestamp retract_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp created_at = 8;
}

message GetMemoRequest {
  int64 id = 1;
  int64 organization_id = 7;
}

message GetMemoResponse {
  Memo memo = 1;
}

message ListMemosRequest {
  optional Filter filter = 1;
  optional string page_token = 2;
  optional int32 page_size = 3;
  repeated OrderBy order_by = 4;

  message Filter {
    oneof kind {
      CompositeFilter composite_filter = 1;
      FieldFilter field_filter = 2;
    }
  }

  message CompositeFilter {
    cloudbeds.rpc.v1.LogicalOperator operator = 1;
    repeated Filter filters = 2;
  }

  message FieldFilter {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_PROPERTY_ID = 1;
      FIELD_PUBLISH_AT = 2;
      FIELD_ID = 3;
    }

    cloudbeds.rpc.v1.ConditionOperator operator = 1;
    Field field = 2;
    cloudbeds.type.v1.Value value = 3;
  }

  message OrderBy {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_PUBLISH_AT = 1;
      FIELD_CREATED_AT = 2;
    }

    Field field = 1;
    cloudbeds.rpc.v1.Direction direction = 2;
  }
}

message ListMemosResponse {
  repeated Memo memos = 1;
  optional string next_page_token = 2;
  optional int32 total_size = 3;
}

message UpdateMemoRequest {
  int64 id = 1;
  int64 organization_id = 2;
  repeated int64 property_ids = 3;
  optional string content = 4;
  optional google.protobuf.Timestamp publish_at = 5;
  optional google.protobuf.Timestamp retract_at = 6;
  
}

message UpdateMemoResponse {
  Memo memo = 1;
}

message DeleteMemoRequest {
  int64 id = 1;
  int64 organization_id = 2;
}

message DeleteMemoResponse {
}

message CreateMemoRequest {
  int64 organization_id = 2;
  repeated int64 property_ids = 3;
  optional string content = 4;
  optional google.protobuf.Timestamp publish_at = 5;
  optional google.protobuf.Timestamp retract_at = 6;  
}

message CreateMemoResponse {
  Memo memo = 1;
}
