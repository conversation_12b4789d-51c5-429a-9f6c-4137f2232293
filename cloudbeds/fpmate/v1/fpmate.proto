syntax = "proto3";

package cloudbeds.fpmate.v1;

option go_package = "github.com/cloudbeds/protos-go/fpmate/v1;fpmate";
option java_package = "com.cloudbeds.fpmate.v1";
option java_outer_classname = "FPMateProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\FPMate\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\FPMate\\V1\\GPBMetadata";

message Receipt {
  repeated ReceiptItem items = 1;
  SubTotalAdjustment adjustments = 2;
  ReceiptTotal receipt_total = 3;
}

message ReceiptItem {
  string operation = 1;
  string description = 2;
  string quantity = 3;
  string unit_price = 4;
  string department = 5;
  string justification = 6;
}

message ReceiptTotal {
  string description = 1;
  string amount = 2;
  string type = 3;
}

message SubTotalAdjustment{
  string description = 1;
  string type = 2;
  string amount = 3;
  string department = 4;
}

message VoidReceipt {
  repeated ReceiptItem items = 1;
}
