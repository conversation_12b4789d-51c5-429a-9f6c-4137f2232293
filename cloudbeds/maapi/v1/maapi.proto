syntax = "proto3";

package cloudbeds.maapi.v1;

option go_package = "github.com/cloudbeds/protos-go/maapi/v1;maapi";
option java_multiple_files = true;
option java_package = "com.cloudbeds.maapi.v1";
option php_namespace = "Cloudbeds\\Protos\\Maapi\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Maapi\\V1\\GPBMetadata";

service MAAPIService {
  rpc HealthCheck (HealthCheckRequest) returns (HealthCheckResponse);
  rpc PropertyChannelList (PropertyChannelListRequest) returns (PropertyChannelListResponse);
  rpc ChannelLoginOAuth2AuthURL (ChannelLoginOAuth2AuthURLRequest) returns (ChannelLoginOAuth2AuthURLResponse);
  rpc ChannelLoginOAuth2Result (ChannelLoginOAuth2ResultRequest) returns (ChannelLoginOAuth2ResultResponse);
  rpc ChannelLoginList (ChannelLoginListRequest) returns (ChannelLoginListResponse);
  rpc ChannelConnectedProperties (ChannelConnectedPropertiesRequest) returns (ChannelConnectedPropertiesResponse);
  rpc GlobalFeatureInfo (GlobalFeatureInfoRequest) returns (GlobalFeatureInfoResponse);
  rpc GlobalFeatureToggle (GlobalFeatureToggleRequest) returns (GlobalFeatureToggleResponse);
  rpc ReviewsImport (ReviewsImportRequest) returns (ReviewsImportResponse);
}

message HealthCheckRequest {}

message HealthCheckResponse {}

message PropertyChannelListRequest {
  string property_id = 1; // MA property id
  string channel_id = 2; // MA channel id
  string vendor_id = 3; // ondeficar or whistle
  string cb_property_id = 4;
}

message PropertyChannelListResponse {
  bool success = 1;
  repeated PropList properties = 2;
  Error error = 3;
}

message ChannelLoginOAuth2AuthURLRequest {
  string property_id = 1; // MA property id
  string channel_id = 2; // MA channel id
  string vendor_id = 3;
  string cb_property_id = 4;
}

message ChannelLoginOAuth2AuthURLResponse {
  bool success = 1;
  string auth_url = 2;
  string attempt_id = 3;
  Error error = 4;
}

message ChannelLoginOAuth2ResultRequest {
  string channel_id = 1;
  string attempt_id = 2;
  string vendor_id = 3;
  string cb_property_id = 4;
}

message ChannelLoginOAuth2ResultResponse {
  bool success = 1;
  Error error = 2;
  OAuth2Result oauth2result = 3;
}

message ChannelLoginListRequest {
  string property_id = 1;
  string channel = 2;
  string vendor_id = 3;
  string cb_property_id = 4;
}

message ChannelLoginListResponse {
  bool success = 1;
  map<string, string> channel_login = 2;
}

message ChannelConnectedPropertiesRequest {
  string channel = 1;
  string vendor_id = 2;
}

message ChannelConnectedPropertiesResponse {
  bool success = 1;
  string channel = 2;
  repeated PropertyStatuses property_list = 3;
  Error error = 4;
}

message GlobalFeatureInfoRequest {
  string property_id = 1;
  string cb_property_id = 2;
}

message GlobalFeatureInfoResponse {
  bool success = 1;
  map<string, Feature> features = 2;
  Error error = 3;
}

message GlobalFeatureToggleRequest {
  string property_id = 1;
  map<string, bool> features = 2; // feature_name => true/false
  bool set_state_of_omitted_features_to = 3;
  string cb_property_id = 4;
}

message GlobalFeatureToggleResponse {
  bool success = 1;
  Error error = 2;
}

message ReviewsImportRequest {
  string property_id = 1;
  string channel = 2;
  int32 limit = 3;
  string cb_property_id = 4;
}

message ReviewsImportResponse {
  bool success = 1;
  Error error = 2;
}

message PropertyStatuses {
  string channel_status      = 1;
  string cb_property_id      = 2;
  string ma_property_id      = 3;
  string channel_property_id = 4;
}

message Feature {
  string name = 1;
  string code = 2;
  bool state = 3;
  bool enabled_by_default = 4;
  map<string, bool> toggleable = 5; // entity => true/false
  repeated string unpassed_conditions = 6;
}

message PropList {
  map<string, ChStatus> channels = 1; // map of channel_id to ChStatus object
  string property_id = 2;
}

message ChStatus {
  string status = 1;
  string currency = 2;
}

message OAuth2Result {
  bool success = 1;
  string error = 2;
  string error_description = 3;
}

message Error {
  string code = 1;
  string message = 2;
  string type = 3;
}
