syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

package cloudbeds.booking.v1;

option go_package = "github.com/cloudbeds/protos-go/booking/v1;booking";
option java_package = "com.cloudbeds.booking.v1";
option java_outer_classname = "BookingProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Booking\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Booking\\V1\\GPBMetadata";


message Booking {
  int64 id = 1;
  int32 num_rooms = 2;
  int64 property_id = 3;
  string booking_type = 4;
  BookingVia booking_via = 5;
  int64 customer_id = 6;
  string customer_name = 7;
  optional int64 group_profile_id = 8;
  optional string group_profile_name = 9;
  optional int64 allotment_block_id = 10;
  optional string allotment_block_name = 11;
  optional int64 folio_config_id = 12;
  string first_name = 13;
  string last_name = 14;
  optional YesNo first_time = 15;
  optional string special_requests = 16;
  optional string identifier = 17;
  optional string third_party_identifier = 18;
  optional google.type.Date booking_date = 19;
  optional google.type.Date checkin_date = 20;
  optional google.type.Date checkout_date = 21;
  BookingStatus status = 22;
  string total = 23;
  string grand_total = 24;
  string room_rate_total_adjustment = 25;
  string room_revenue_total_adjustment = 26;
  string room_revenue_total = 27;
  DiscountType discount_type = 28;
  optional string discount_value = 29;
  bool is_custom = 30;
  string taxes_value = 31;
  string taxes_sub_value = 32;
  int64 policy_id = 33;
  bool third_party = 34;
  int64 source = 35;
  bool is_root_source = 36;
  optional int64 ota_source_id = 37;
  optional int64 parent_source = 38;
  string source_code = 39;
  bool is_hotel_collect_booking = 40;
  int64 association_id = 41;
  string source_commission = 42;
  optional string association_fee_value = 43;
  optional AssociationFeeType association_fee_type = 44;
  string source_commission_value = 45;
  optional string cancellation_numb = 46;
  optional string cancellation_date = 47;
  string additional_charges = 48;
  string payment = 49;
  optional string credit_cards = 50;
  string paid_value = 51;
  string booking_deposit = 52;
  optional string hear_about = 53;
  string commission_percentage = 54;
  optional string last_change = 55;
  bool delete = 56;
  optional string auto_change_date = 57;
  optional string additional_guests = 58;
  string prolongation_day = 59;
  string document_number = 60;
  string taxes_ids = 61;
  string fees_ids = 62;
  string currency_from = 63;
  string currency_to = 64;
  string currency_rate = 65;
  optional string imported_at = 66;
  optional string booking_estimated_arrival_time = 67;
  bool is_demo = 68;
  optional int32 adults_number = 69;
  optional int32 kids_number = 70;
  optional string lang = 71;
  string revenue = 72;
  int32 rounding_method = 73;
  optional google.protobuf.Timestamp created_at = 74;
  optional google.protobuf.Timestamp updated_at = 75;
  optional int64 org_guest_id = 76;
  optional string taxes_total_adjustment = 77;
  optional int64 segment_id = 78;
}

enum BookingVia {
  BOOKING_VIA_UNSPECIFIED = 0;
  BOOKING_VIA_WEB = 1;
  BOOKING_VIA_FACEBOOK = 2;
  BOOKING_VIA_API = 3;
  BOOKING_VIA_DASHBOARD = 4;
  BOOKING_VIA_THIRD_PARTY = 5;
  BOOKING_VIA_MYALLOCATOR = 6;
  BOOKING_VIA_IMPORT = 7;
}

enum YesNo {
  YES_NO_UNSPECIFIED = 0;
  YES_NO_Y = 1;
  YES_NO_N = 2;
}

enum BookingStatus {
  BOOKING_STATUS_UNSPECIFIED = 0;
  BOOKING_STATUS_IN_PROGRESS = 1;
  BOOKING_STATUS_CALL2CONFIRM = 2;
  BOOKING_STATUS_CONFIRMED = 3;
  BOOKING_STATUS_CANCELED = 4;
  BOOKING_STATUS_CHECKED_IN = 5;
  BOOKING_STATUS_CHECKED_OUT = 6;
  BOOKING_STATUS_NOT_CONFIRMED = 7;
  BOOKING_STATUS_NO_SHOW = 8;
  BOOKING_STATUS_PAYPAL_INIT = 9;
  BOOKING_STATUS_PENDING_PAYMENT = 10;
}

enum DiscountType {
  DISCOUNT_TYPE_UNSPECIFIED = 0;
  DISCOUNT_TYPE_NONE = 1;
  DISCOUNT_TYPE_PERCENTAGE = 2;
  DISCOUNT_TYPE_FLAT = 3;
}

enum AssociationFeeType {
  ASSOCIATION_FEE_TYPE_UNSPECIFIED = 0;
  ASSOCIATION_FEE_TYPE_COMMISSION = 1;
  ASSOCIATION_FEE_TYPE_PER_NIGHT = 2;
  ASSOCIATION_FEE_TYPE_PER_ROOM = 3;
}