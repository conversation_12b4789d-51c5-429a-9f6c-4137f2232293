syntax = "proto3";

import "cloudbeds/booking/v1/booking.proto";
import "cloudbeds/booking/v1/booking_guest.proto";
import "cloudbeds/booking/v1/booking_room.proto";
import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";

package cloudbeds.booking.v1;

option go_package = "github.com/cloudbeds/protos-go/booking/v1;booking";
option java_package = "com.cloudbeds.booking.v1";
option java_outer_classname = "BookingServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Booking\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Booking\\V1\\GPBMetadata";


service BookingService {
    rpc ListBookings(ListBookingsRequest) returns (ListBookingsResponse);
}

message ListBookingsResponse {
  repeated BookingWithRooms bookings = 1;
  string next_page_token = 2;
}

message BookingWithRooms {
  Booking booking = 1;
  repeated BookingRoom rooms = 2;
  repeated BookingGuest guests = 3;
}


message OrderBy {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_ID = 1;
    FIELD_IDENTIFIER = 2;
  }

  Field field = 1;
  cloudbeds.rpc.v1.Direction direction = 2;
}

message ListBookingsRequest {
    repeated int64 property_ids = 1;
    optional Filter filter = 2;
    optional string page_token = 3;
    optional int32 page_size = 4;
    repeated OrderBy order_by = 5;
    optional bool include_rooms = 6;
    optional bool include_guests = 7;

    message Filter {
        oneof kind {
            CompositeFilter composite_filter = 1;
            FieldFilter field_filter = 2;
        }
    }

    message CompositeFilter {
        cloudbeds.rpc.v1.LogicalOperator operator = 1;
        repeated Filter filters = 2;
    }


    message FieldFilter {
        enum Field {
            FIELD_UNSPECIFIED = 0;
            FIELD_ID = 1; // Expecting number value
            FIELD_IDENTIFIER = 2; // Expecting string value
        }

        cloudbeds.rpc.v1.ConditionOperator operator = 1;
        Field field = 2;
        cloudbeds.type.v1.Value value = 3;
    }
}