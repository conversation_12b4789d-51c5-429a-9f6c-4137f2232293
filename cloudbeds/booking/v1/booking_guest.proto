syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

package cloudbeds.booking.v1;

option go_package = "github.com/cloudbeds/protos-go/booking/v1;booking";
option java_package = "com.cloudbeds.booking.v1";
option java_outer_classname = "BookingGuestProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Booking\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Booking\\V1\\GPBMetadata";

message BookingGuest {
  int64 id = 1;
  int64 property_id = 2;
  string first_name = 3;
  string last_name = 4;
  int32 count_reservations = 5;
  bool is_repeat_guest = 6;
  string email = 7;
  google.type.Date birthday = 8;
  string cpf = 9;
  string phone = 10;
  Gender gender = 11;
  string cell_phone = 12;
  string street = 13;
  string number = 14;
  string complement = 15;
  string neighborhood = 16;
  string city = 17;
  string state = 18;
  string zip = 19;
  string country = 20;
  string rg = 21;
  google.type.Date issue_date = 22;
  string issuer = 23;
  string issuer_state = 24;
  int64 user_id = 25;
  string address1 = 26;
  string address2 = 27;
  int64 id_photo = 28;
  bool deleted = 29;
  int64 status_id = 30;
  string status_name = 31;
  DocumentType document_type = 32;
  string document_number = 33;
  google.type.Date document_issue_date = 34;
  string document_issuing_country = 35;
  google.type.Date document_expiration_date = 36;
  google.protobuf.Timestamp last_change = 37;
  bool is_opt_in = 38;
  string opt_in_hash = 39;
  string email_hash = 40;
  bool is_anonymized = 41;
  string guest_tax_id_number = 42;
  string company_name = 43;
  string company_tax_id_number = 44;
  bool is_merged = 45;
  int64 org_guest_id = 46;
  bool is_main_guest = 47;
}

enum Gender {
  GENDER_UNSPECIFIED = 0;
  GENDER_M = 1;
  GENDER_F = 2;
  GENDER_NA = 3;
}

enum DocumentType {
  DOCUMENT_TYPE_UNSPECIFIED = 0;
  DOCUMENT_TYPE_NA = 1;
  DOCUMENT_TYPE_DRIVER_LICENCE = 2;
  DOCUMENT_TYPE_SOCIAL_SECURITY_CARD = 3;
  DOCUMENT_TYPE_STUDENT_ID = 4;
  DOCUMENT_TYPE_PASSPORT = 5;
  DOCUMENT_TYPE_DNI = 6;
  DOCUMENT_TYPE_NIE = 7;
}
