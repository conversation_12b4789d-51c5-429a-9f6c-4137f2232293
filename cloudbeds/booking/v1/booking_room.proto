syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

package cloudbeds.booking.v1;

option go_package = "github.com/cloudbeds/protos-go/booking/v1;booking";
option java_package = "com.cloudbeds.booking.v1";
option java_outer_classname = "BookingRoomProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Booking\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Booking\\V1\\GPBMetadata";

message DetailedRate {
  google.type.Date date = 1;
  string rate = 2;
  int32 adults = 3;
  int32 kids = 4;
  optional string rate_description = 5;
}

message BookingRoom {
  int64 id = 1;
  int64 booking_id = 2;
  string room_identifier = 3;
  int32 room_group = 4;
  bool in_house = 5;
  optional int64 parent_id = 6;
  optional string room_id = 7;
  optional int64 event_id = 8;
  int32 adults = 9;
  int32 kids = 10;
  google.type.Date start_date = 11;
  google.type.Date end_date = 12;
  int64 room_type_id = 13;
  string room_total = 14;
  optional string room_type_name = 15;
  string min_room_rate = 16;
  string max_room_rate = 17;
  string total = 18;
  repeated DetailedRate detailed_rates = 19;
  bool adults_in_base_price = 20;
  bool children_in_base_price = 21;
  string charge_additional_adult = 22;
  string charge_additional_child = 23;
  string add_adult_rate_first_night = 24;
  string add_adult_total = 25;
  string add_kid_rate_first_night = 26;
  string add_kids_total = 27;
  int64 package_id = 28;
  int64 guest_id = 29;
  optional string guest_first_name = 30;
  optional string guest_last_name = 31;
  optional int64 rate_id = 32;
  optional bool is_room_locked = 33;
  google.protobuf.Timestamp created_at = 34;
  google.protobuf.Timestamp updated_at = 35;
  optional bool is_breakfast_included = 36;
  optional bool has_non_refundable_rate = 37;
}
