syntax = "proto3";

import "google/type/latlng.proto";

package cloudbeds.organization.v1;

option go_package = "github.com/cloudbeds/protos-go/organization/v1;organization";
option java_package = "com.cloudbeds.organization.v1";
option java_outer_classname = "TypesProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Organization\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Organization\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";

message IslandConfiguration {
  // The unique island identifier. Ex: "us1".
  string id = 1;

  // The url for MFD. This will respect any configured organization subdomain.
  string mfd_url = 2;

  // The url for the public API for this island.
  string api_url = 3;

  // The url to access the CRM for this island.
  string crm_url = 4;
}

message Organization {
  uint64 id = 1;
  string name = 2;
  string sub_domain = 3;
  bool is_multi_prop = 4;
  bool is_my_organization_enabled = 5;
  bool is_active = 6;
  bool is_deleted = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  IslandConfiguration island = 10;
  repeated OrganizationProperty properties = 11;
}

message OrganizationProperty {
  uint64 id = 1;
  string name = 2;
  IslandConfiguration island = 3;
}

message Property {
  uint64 id = 1;
  string name = 2;
  optional int32 type = 3;
  optional string lang = 4;
  optional string currency = 5;
  optional string timezone = 6;
  optional string date_format = 7;
  optional string time_format = 8;
  bool is_active = 9;
  bool is_deleted = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  IslandConfiguration island = 13;
  optional Organization organization = 14;

  // NOTE: The profile is rather large, so must be specifically requested in list/search APIs.
  optional PropertyProfile property_profile = 21;
}

enum Status {
  STATUS_UNSPECIFIED = 0;
  STATUS_REGULAR = 1;
  STATUS_EXPIRED = 2;
  STATUS_DEV = 3;
  STATUS_DUMMY = 4;
  STATUS_CANCELED = 5;
  STATUS_INACTIVE_NO_INTEGRATIONS = 6;
  STATUS_SUSPENDED = 7;
  STATUS_DEMO = 8;
  STATUS_NOTICE_MINUS_1 = 9;
  STATUS_NOTICE_1 = 10;
  STATUS_NOTICE_7 = 11;
  STATUS_NOTICE_14 = 12;
  STATUS_CANCELLATION_PENDING = 13;
  STATUS_PENDING_ACTIVATION = 14;
}

message PropertyProfile {
  // The property ID.
  uint64 id = 1;
  optional string business_name = 2;
  optional Address business_address = 3;
  optional string hotel_name = 4;
  optional Address hotel_address = 5;
  optional string expiration_date = 6;
  Status status = 7;
  optional string hotel_phone = 8;
  optional string hotel_fax = 9;
  optional string website_url = 10;
  optional bool is_dev = 11;
  optional string myallocator_platform = 12;
}

message Address {
  string address1 = 1;
  string address2 = 2;
  optional string street = 3;
  optional string number = 4;
  optional string complement = 5;
  optional string neighborhood = 6;
  optional string city = 7;
  optional string state = 8;
  optional string zip = 9;
  string country_code = 10;
  optional string email = 11;
  optional google.type.LatLng coordinates = 12;
}

enum Order {
  ORDER_UNSPECIFIED = 0;
  ORDER_ASC = 1;
  ORDER_DESC = 2;
}

message Pagination {
  uint32 limit = 1;
  uint32 offset = 2;
}

message PropertyFeature {
  uint64 property_id = 2;
  string name = 3;
  bool enabled = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}
