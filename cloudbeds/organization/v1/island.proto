syntax = "proto3";

package cloudbeds.organization.v1;

import "cloudbeds/organization/v1/common.proto";

option go_package = "github.com/cloudbeds/protos-go/organization/v1;organization";
option java_package = "com.cloudbeds.organization.v1";
option java_outer_classname = "IslandConfigurationProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Organization\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Organization\\V1\\GPBMetadata";

service IslandConfigurationService {
  rpc ListIslandConfigurations (ListIslandConfigurationsRequest) returns (ListIslandConfigurationsResponse);
}

message ListIslandConfigurationsRequest {}
message ListIslandConfigurationsResponse {
    repeated IslandConfiguration islands = 1;
}
