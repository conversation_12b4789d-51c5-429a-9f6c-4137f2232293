syntax = "proto3";

package cloudbeds.organization.v1;

import "cloudbeds/organization/v1/common.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/organization/v1;organization";
option java_package = "com.cloudbeds.organization.v1";
option java_outer_classname = "PropertyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Organization\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Organization\\V1\\GPBMetadata";

service PropertyService {
  rpc GetProperty (GetPropertyRequest) returns (GetPropertyResponse);
  rpc CreateProperty (CreatePropertyRequest) returns (CreatePropertyResponse);
  rpc UpdateProperty (UpdatePropertyRequest) returns (UpdatePropertyResponse);
  rpc DeleteProperty (DeletePropertyRequest) returns (DeletePropertyResponse);
  rpc ListProperties (ListPropertiesRequest) returns (ListPropertiesResponse);
  rpc SearchProperties (SearchPropertiesRequest) returns (SearchPropertiesResponse);
  rpc GetPropertyProfile (GetPropertyProfileRequest) returns (GetPropertyProfileResponse);
  rpc GetPropertyFeature (GetPropertyFeatureRequest) returns (GetPropertyFeatureResponse);
  rpc ListPropertyFeatures (ListPropertyFeaturesRequest) returns (ListPropertyFeaturesResponse);
  rpc ListPropertyFeaturesByNames (ListPropertyFeaturesByNamesRequest) returns (ListPropertyFeaturesByNamesResponse);
}

message CreatePropertyRequest {
  string name = 1;
  bool is_active = 2;
  uint64 owner_organization_id = 3;
  optional int32 type = 4;
  optional string lang = 5;
  optional string currency = 6;
  optional string timezone = 7;
  optional string date_format = 8;
  optional string time_format = 9;
  string island = 10;
}

message CreatePropertyResponse {
  Property property = 1;
}

message UpdatePropertyRequest {
  uint64 id = 1;
  string name = 2;
  bool is_active = 3;
  uint64 owner_organization_id = 4;
}

message UpdatePropertyResponse {
  Property property = 1;
}

message ListPropertiesRequest {
  optional uint64 organization_id = 1;
  optional string name = 2;
  optional bool is_active = 3;
  optional bool is_deleted = 4;
  repeated uint64 ids = 5;
  bool include_profile = 6;
}

message ListPropertiesResponse {
  repeated Property properties = 1;
}

message GetPropertyRequest {
  uint64 id = 1;
}

message GetPropertyResponse {
  Property property = 1;
}

message ListPropertyFeaturesRequest {
  uint64 property_id = 1;
  optional bool enabled = 2;
}

message ListPropertyFeaturesResponse {
  repeated PropertyFeature property_features = 1;
}

message ListPropertyFeaturesByNamesRequest {
  repeated string feature_names = 1;
  Pagination pagination = 2;
}

message ListPropertyFeaturesByNamesResponse {
  repeated PropertyFeature property_features = 1;
}


message GetPropertyFeatureRequest {
  uint64 property_id = 1;
  string name = 2;
}

message GetPropertyFeatureResponse {
  PropertyFeature property_feature = 1;
}

enum SortField {
  SORT_FIELD_UNSPECIFIED = 0;
  SORT_FIELD_ID = 1;
  SORT_FIELD_NAME = 2;
  SORT_FIELD_STATUS = 3;
  SORT_FIELD_CREATED_AT = 4;
  SORT_FIELD_MODIFIED_AT = 5;
}

message Sort {
  SortField sort_field = 1;
  Order order_field = 2;
}

message SearchPropertiesRequest {
  optional uint64 property_id = 1;
  optional string name = 2;
  optional Status status = 3;
  optional string city_name = 4;
  optional string state_name = 5;
  optional string country_code = 6;
  optional google.protobuf.Timestamp created_from = 7;
  optional google.protobuf.Timestamp created_to = 8;
  repeated Sort sorting = 9;
  Pagination pagination = 10;
  optional string island = 11;
  bool include_profile = 12;
  optional uint64 ma_property_id = 13 [deprecated = true]; // Deprecated
  repeated uint64 property_ids = 14;
}

message SearchPropertiesResponse {
  repeated Property properties = 1;
}

message DeletePropertyRequest {
  uint64 id = 1;
}

message DeletePropertyResponse {}

message GetPropertyProfileRequest {
  uint64 id = 1;
}

message GetPropertyProfileResponse {
  PropertyProfile property_profile = 1;
}
