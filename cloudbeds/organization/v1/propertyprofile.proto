syntax = "proto3";

package cloudbeds.organization.v1;

import "cloudbeds/organization/v1/common.proto";

option go_package = "github.com/cloudbeds/protos-go/organization/v1;organization";
option java_package = "com.cloudbeds.organization.v1";
option java_outer_classname = "PropertyProfileProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Organization\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Organization\\V1\\GPBMetadata";

service PropertyProfileService {
  rpc GetPropertyProfile (PropertyProfileServiceGetPropertyProfileRequest) returns (PropertyProfileServiceGetPropertyProfileResponse);
}

message PropertyProfileServiceGetPropertyProfileRequest {
  uint64 property_id = 1;
}

message PropertyProfileServiceGetPropertyProfileResponse {
  PropertyProfile property_profile = 1;
}