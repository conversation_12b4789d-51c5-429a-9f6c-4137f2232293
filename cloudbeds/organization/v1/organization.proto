syntax = "proto3";

package cloudbeds.organization.v1;

import "cloudbeds/organization/v1/common.proto";

option go_package = "github.com/cloudbeds/protos-go/organization/v1;organization";
option java_package = "com.cloudbeds.organization.v1";
option java_outer_classname = "OrganizationProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Organization\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Organization\\V1\\GPBMetadata";

service OrganizationService {
  rpc CreateOrganization (CreateOrganizationRequest) returns (CreateOrganizationResponse);
  rpc UpdateOrganization (UpdateOrganizationRequest) returns (UpdateOrganizationResponse);
  rpc ListOrganizations (ListOrganizationsRequest) returns (ListOrganizationsResponse);
  rpc SearchOrganizations (SearchOrganizationsRequest) returns (SearchOrganizationsResponse);
  rpc GetOrganization (GetOrganizationRequest) returns (GetOrganizationResponse);
  rpc DeleteOrganization (DeleteOrganizationRequest) returns (DeleteOrganizationResponse);
  rpc ListOrganizationPropertyFeatures (ListOrganizationPropertyFeaturesRequest) returns (ListOrganizationPropertyFeaturesResponse);
  rpc GetOrganizationSettings (GetOrganizationSettingsRequest) returns (GetOrganizationSettingsResponse);
  rpc UpdateOrganizationSettings (UpdateOrganizationSettingsRequest) returns (UpdateOrganizationSettingsResponse);
}

message CreateOrganizationRequest {
  string name = 1;
  bool is_active = 2;
  string sub_domain = 3;
  optional bool is_multi_prop = 4;
  optional bool is_my_organization_enabled = 5;
  string island = 6;
}

message CreateOrganizationResponse {
  Organization organization = 1;
}

message UpdateOrganizationRequest {
  uint64 id = 1;
  optional string name = 2;
  optional bool is_active = 3;
  optional string sub_domain = 4;
  optional bool is_multi_prop = 5;
  optional bool is_my_organization_enabled = 6;
}

message UpdateOrganizationResponse {
  Organization organization = 1;
}

message ListOrganizationsRequest {
  optional string name = 1;
  optional bool is_active = 2;
  optional bool is_deleted = 3;
  repeated uint64 ids = 4;
}

enum OrganizationSortField {
  ORGANIZATION_SORT_FIELD_UNSPECIFIED = 0;
  ORGANIZATION_SORT_FIELD_ID = 1;
  ORGANIZATION_SORT_FIELD_NAME = 2;
}

message OrganizationSort {
  OrganizationSortField sort_field = 1;
  Order order_field = 2;
}

message SearchOrganizationsRequest {
  optional uint64 organization_id = 1;
  optional string name = 2;
  optional uint64 property_id = 3;
  repeated OrganizationSort sorting = 4;
  Pagination pagination = 5;
  optional string island = 6;
}

message ListOrganizationsResponse {
  repeated Organization organizations = 1;
}

message SearchOrganizationsResponse {
  repeated Organization organizations = 1;
}

message GetOrganizationRequest {
  uint64 id = 1;
}

message GetOrganizationResponse {
  Organization organization = 1;
}

message DeleteOrganizationRequest {
  uint64 id = 1;
}

message DeleteOrganizationResponse {}

message ListOrganizationPropertyFeaturesRequest {
  // The organization id
  uint64 id = 1;

  // Optional filter by name(s)
  repeated string feature_names = 2;

  // Optional filter by enabled status
  optional bool enabled = 3;
}

message ListOrganizationPropertyFeaturesResponse {
  repeated PropertyFeature property_features = 1;
}

message GetOrganizationSettingsRequest {
  uint64 id = 1;
}

message MultiViewSettings {
  bool enabled_for_reservations_page = 1;
  bool enabled_for_create_reservation_page = 2;
  bool enabled_for_dashboard_page = 3;
  bool enabled_for_availability_matrix_page = 4;
}

message GetOrganizationSettingsResponse {
  MultiViewSettings multi_view_settings = 1;
}

message UpdateOrganizationSettingsRequest {
  uint64 id = 1;
  MultiViewSettings multi_view_settings = 2;
}

message UpdateOrganizationSettingsResponse {}