syntax = "proto3";

package cloudbeds.authentication.v1;

option go_package = "github.com/cloudbeds/protos-go/authentication/v1;authentication";
option java_package = "com.cloudbeds.authentication.v1";
option java_outer_classname = "ApiKeyServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Authentication\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Authentication\\V1\\GPBMetadata";

import "cloudbeds/authentication/v1/api_key.proto";
import "google/protobuf/timestamp.proto";

// Service to manage "api keys".
service ApiKeyService {
    rpc ListApiKeys(ListApiKeysRequest) returns (ListApiKeysResponse);
    rpc CreateApiKey(CreateApiKeyRequest) returns (CreateApiKeyResponse);
    rpc UpdateApiKey(UpdateApiKeyRequest) returns (UpdateApiKeyResponse);
    rpc UpdateOauthClient(UpdateOauthClientRequest) returns (UpdateOauthClientResponse);
    rpc RegenerateApiKey(RegenerateApiKeyRequest) returns (RegenerateApiKeyResponse);
    rpc RevokeApiKey(RevokeApiKeyRequest) returns (RevokeApiKeyResponse);
    rpc DeleteApiKey(DeleteApiKeyRequest) returns (DeleteApiKeyResponse);
}

// Request message for listing api keys.
message ListApiKeysRequest {
    // Optional. The OAuth client ID(s) to retrieve ApiKeys for.
    repeated string client_ids = 1;

    // Optional. A list owner ids to retrieve keys for.
    repeated string owner_ids = 2;

    // Optional (required if using owner_ids). The type of owner to query for.
    OwnerType owner_type = 3;

    // Optional. The type of key to query for.
    ApiKeyType type = 4;

    // Optional. List of the API Keys ids to query for.
    repeated uint64 ids = 5;

    // Number of elements to retrieve in a single page.
    int32 page_size = 100;

    // Token of the page to retrieve. If not specified, the first
    // page of results will be returned. Use the value obtained from
    // `next_page_token` in the previous response in order to request
    // the next page of results.
    string page_token = 101;
}

// Response message for listing api keys.
message ListApiKeysResponse {
    repeated ApiKey api_keys = 1;

    // Pagination token used to retrieve the next page of results.
    // Pass the content of this string as the `page_token` attribute of
    // the next request. `next_page_token` is not returned for the last
    // page.
    string next_page_token = 2;
}

message CreateOauthApiKeyData {
    // Required. The Oauth client identifier.
    string client_id = 1;

    // Required. The Oauth client secret.
    string client_secret = 2;

    // Required. The refresh token used to generate access tokens for an oauth backed api key on exchange.
    string refresh_token = 3;

    // Required. The oauth provider.
    OauthProvider provider = 4;
}

// Request message to create a new api key.
message CreateApiKeyRequest {
    // Required. The type of ApiKey.
    ApiKeyType type = 1;

    // Optiona. A description for this ApiKey.
    string description = 2;

    // Optional. The time at which (UTC) this ApiKey expires.
    google.protobuf.Timestamp expires_at = 3;

    // Optional. Identifier for the user that created this ApiKey.
    string created_by = 4;

    // ID of the owner entity for this key.
    string owner_id = 5;

    // Type of owner for this key.
    OwnerType owner_type = 6;

    oneof data {
        CreateOauthApiKeyData oauth_data = 7;
    }

    // Optional (until migration). The authorized property ids for this key.
    repeated int64 authorized_property_ids = 8;
}

// Response message for creating a new api key.
message CreateApiKeyResponse {
    // The ApiKey that was created. This initial response will contain the api key in plaintext but it will be omitted from 
    // subsequent requests.
    NewApiKey api_key = 1;
}

message UpdateApiKeyRequest {
    // The ID of the ApiKey to update.
    uint64 id = 1;

    // (Optional) A description to set for the ApiKey.
    string description = 2;
}

message UpdateApiKeyResponse {}

message UpdateOauthClientRequest {
    // Required, the client to update the secret for.
    string client_id = 1;

    // Required, the client provider.
    OauthProvider provider = 2;

    // Required, the new secret to set.
    string client_secret = 3;
}

message UpdateOauthClientResponse {}

message RegenerateApiKeyRequest {
    // The ID of the ApiKey to regenerate.
    uint64 id = 1;
}

// Response message for regenerating an api key.
message RegenerateApiKeyResponse {
    // The ApiKey that was regenerated. This initial response will contain the ApiKey in plaintext but it will be omitted from 
    // subsequent requests.
    NewApiKey api_key = 1;
}

// Request message to revoke a ApiKey. ApiKeys may not be used again after revocation.
message RevokeApiKeyRequest {
    // The id of the ApiKey to revoke.
    uint64 id = 1;
}

message RevokeApiKeyResponse {}

// Request message to delete a ApiKey. This endpoint is primarily useful for tests and scenarios
// where a hard delete is required.
message DeleteApiKeyRequest {
    // The id of the ApiKey to delete.
    uint64 id = 1;
}

message DeleteApiKeyResponse {}
