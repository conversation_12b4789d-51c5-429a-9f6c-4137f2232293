syntax = "proto3";

package cloudbeds.authentication.v1;

option go_package = "github.com/cloudbeds/protos-go/authentication/v1;authentication";
option java_package = "com.cloudbeds.authentication.v1";
option java_outer_classname = "ApiKeyProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Authentication\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Authentication\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";

enum OauthProvider {
    OAUTH_PROVIDER_UNSPECIFIED = 0;
    OAUTH_PROVIDER_MFD = 1;
}

enum ApiKeyType {
    API_KEY_TYPE_UNSPECIFIED = 0;
    API_KEY_TYPE_OAUTH = 1;
    API_KEY_TYPE_OAUTH_SELF = 2;
}

enum OwnerType {
    OWNER_TYPE_UNSPECIFIED = 0;
    OWNER_TYPE_PROPERTY = 1;
    OWNER_TYPE_ASSOCIATION = 2;
    OWNER_TYPE_USER = 3;
    OWNER_TYPE_PARTNER = 4;
}

// OauthApiKeyConfiguration is a sanitized (no refresh api key/client secret) form on the
// configuration data we store per client/api key.
message OauthApiKeyConfiguration {
    // The OAuth client id.
    string client_id = 1;

    OauthProvider provider = 2;
}

message ApiKey {
    // Unique identifier for this api key.
    uint64 id = 1;

    // (Optional) The api key's description.
    string description = 2;

    // The type of api key.
    ApiKeyType type = 3;

    // Whether the api key is active or suspended.
    bool is_active = 4;

    // When the api key expiries.
    google.protobuf.Timestamp expires_at = 5;

    // When the api key was created.
    google.protobuf.Timestamp created_at = 6;

    // When the api key was last updated.
    google.protobuf.Timestamp updated_at = 7;

    oneof configuration {
        OauthApiKeyConfiguration oauth_configuration = 8;
    }

    // Plaintext version of api key for identification.
    string plaintext = 9;

    // ID of the owner entity for this key.
    string owner_id = 10;

    // Type of owner for this key.
    OwnerType owner_type = 11;

    // Optional (until migration). The authorized property ids for this key.
    repeated int64 authorized_property_ids = 12;
}

// A NewApiKey represents a newly minted api key that carries that raw api key string with it.
message NewApiKey {
    // Unique identifier for this api key.
    uint64 id = 1;

    // The actual key.
    string api_key = 2;

    // (Optional) The api key's description.
    string description = 3;

    // The type of api key.
    ApiKeyType type = 4;

    // When the api key expiries.
    google.protobuf.Timestamp expires_at = 5;

    // When the api key was created.
    google.protobuf.Timestamp created_at = 6;

    // Plaintext version of api key for identification.
    string plaintext = 7;

    // ID of the owner entity for this key.
    string owner_id = 8;

    // Type of owner for this key.
    OwnerType owner_type = 9;
}
