syntax = "proto3";

package cloudbeds.authentication.v1;

option go_package = "github.com/cloudbeds/protos-go/authentication/v1;authentication";
option java_package = "com.cloudbeds.authentication.v1";
option java_outer_classname = "ApiSessionServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Authentication\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Authentication\\V1\\GPBMetadata";

service ApiSessionService {
  rpc CreateResources(CreateResourcesRequest) returns (CreateResourcesResponse);
}

enum Type {
  TYPE_UNSPECIFIED = 0;
  TYPE_PROPERTY = 1;
  TYPE_ASSOCIATION = 2;
}

message Resource {
  Type type = 1;
  uint64 id = 2;
}

message CreateResourcesRequest {
  string user_email = 1;
  string client_id = 2;
  repeated Resource resources = 3;
  string key = 4;
}

message CreateResourcesResponse {
  enum Status {
    STATUS_UNSPECIFIED = 0;
    STATUS_SUCCESS = 1;
    STATUS_FAILURE = 2;
    STATUS_CONFLICT = 3;
  }
  Status status = 1;
  string message = 2;
}
