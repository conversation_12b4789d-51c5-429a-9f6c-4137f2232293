syntax = "proto3";

package cloudbeds.authentication.v1;

option go_package = "github.com/cloudbeds/protos-go/authentication/v1;authentication";
option java_package = "com.cloudbeds.authentication.v1";
option java_outer_classname = "AdminServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Authentication\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Authentication\\V1\\GPBMetadata";

service AdminService {
  rpc CreateClient(CreateClientRequest) returns (CreateClientResponse);
  rpc UpdateClient(UpdateClientRequest) returns (UpdateClientResponse);
  rpc UpdateClientScopes(UpdateClientScopesRequest) returns (UpdateClientScopesResponse);
  rpc UpdateClientSecret(UpdateClientSecretRequest) returns (UpdateClientSecretResponse);
  rpc DeleteClient(DeleteClientRequest) returns (DeleteClientResponse);
}

message Client {
  string id = 1;
  string type = 2;
  repeated string scopes = 3;
}

message CreateClientRequest {
  string id = 1;
  string secret = 2;
  string type = 3;
  repeated string scopes = 4;
}

message CreateClientResponse {
  Client client = 1;
}

message UpdateClientRequest {
  string id = 1;
  optional string type = 2;
}

message UpdateClientResponse {
  Client client = 1;
}

message UpdateClientScopesRequest {
  string id = 1;
  repeated string scopes = 2;
}

message UpdateClientScopesResponse {
  Client client = 1;
}

message UpdateClientSecretRequest {
  string id = 1;
  string secret = 2;
}

message UpdateClientSecretResponse {
  Client client = 1;
}

message DeleteClientRequest {
  string id = 1;
}

message DeleteClientResponse {
}
