syntax = "proto3";

package cloudbeds.authentication.v1;

option go_package = "github.com/cloudbeds/protos-go/authentication/v1;authentication";
option java_package = "com.cloudbeds.authentication.v1";
option java_outer_classname = "JwtServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Authentication\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Authentication\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

service JwtService {
  rpc CreateToken(CreateTokenRequest) returns (CreateTokenResponse);
}

message Claim {
  string key = 1;
  google.protobuf.Value value = 2;
}

message CreateTokenRequest {
  string client_id = 1;
  string client_secret = 2;
  repeated Claim claims = 3;
}

message CreateTokenResponse {
  string token = 1;
  google.protobuf.Timestamp expires_at = 2;
}
