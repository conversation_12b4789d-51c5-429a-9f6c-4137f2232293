syntax = "proto3";

import "google/protobuf/timestamp.proto";

package cloudbeds.group.v1;

option go_package = "github.com/cloudbeds/protos-go/group/v1;group";
option java_package = "com.cloudbeds.group.v1";
option java_outer_classname = "GroupProfileContactProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Group\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Group\\V1\\GPBMetadata";

message ContactDetail {
  string type = 1;   // e.g., "business", "personal", "home", etc.
  string value = 2;  // e.g., "<EMAIL>" or "+*********"
}

message GroupProfileContact {
  uint64 id = 1;
  uint64 group_profile_id = 2;
  bool primary = 3;
  bool anonymized = 4;
  string prefix = 5;
  string first_name = 6;
  string last_name = 7;
  repeated ContactDetail emails = 8;
  repeated ContactDetail phones = 9;
  string status = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  string address_1 = 13;
  string address_2 = 14;
  string city = 15;
  string state = 16;
  string country_code = 17;
  string zip = 18;
  string role = 19;
}