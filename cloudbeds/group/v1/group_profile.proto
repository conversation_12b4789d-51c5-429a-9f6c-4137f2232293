syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

package cloudbeds.group.v1;

option go_package = "github.com/cloudbeds/protos-go/group/v1;group";
option java_package = "com.cloudbeds.group.v1";
option java_outer_classname = "GroupProfileProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Group\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Group\\V1\\GPBMetadata";


message GroupProfile {
  uint64 id = 1;
  uint64 property_id = 2;
  int32 allotment_block_count = 3;
  string name = 4;
  string code = 5;
  google.type.Date earliest_checkin_at = 6;
  google.type.Date latest_checkout_at = 7;
  string status = 8;
  uint64 folio_config_id = 9;
  bool transaction_mode = 10;
  string transaction_types = 11;
  string source_code = 12;
  string total = 13;        // Use string for precision-safe decimal
  string grand_total = 14;
  string paid_value = 15;
  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 17;
  string address_1 = 18;
  string address_2 = 19;
  string city = 20;
  string state = 21;
  string country_code = 22;
  string zip = 23;
  string commission_type = 24;
  string commission_amount = 25;
  bool enable_aggregate_allotment_block = 26;
  uint64 aggregate_allotment_block_package_id = 27;
}