syntax = "proto3";

import "cloudbeds/group/v1/group_profile.proto";
import "cloudbeds/group/v1/group_profile_contact.proto";
import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";

package cloudbeds.group.v1;

option go_package = "github.com/cloudbeds/protos-go/group/v1;group";
option java_package = "com.cloudbeds.group.v1";
option java_outer_classname = "GroupProfileServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Group\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Group\\V1\\GPBMetadata";


service GroupProfileService {
    rpc ListGroups(ListGroupsRequest) returns (ListGroupsResponse);
    rpc ListContactById(ListContactByIdRequest) returns (ListContactByIdResponse);
}

message ListGroupsResponse {
    repeated GroupProfile groups = 1;
    string next_page_token = 2;
}

message OrderBy {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_ID = 1;
    FIELD_GROUP_CODE = 2;
  }

  Field field = 1;
  cloudbeds.rpc.v1.Direction direction = 2;
}

message ListGroupsRequest {
    repeated int64 property_ids = 1;
    optional Filter filter = 2;
    optional string page_token = 3;
    optional int32 page_size = 4;
    repeated OrderBy order_by = 5;

    message Filter {
        oneof kind {
            CompositeFilter composite_filter = 1;
            FieldFilter field_filter = 2;
        }
    }

    message CompositeFilter {
        cloudbeds.rpc.v1.LogicalOperator operator = 1;
        repeated Filter filters = 2;
    }


    message FieldFilter {
        enum Field {
            FIELD_UNSPECIFIED = 0;
            FIELD_ID = 1; // Expecting number value
            FIELD_GROUP_CODE = 2; //Expecting string value with code (g1234)
        }

        cloudbeds.rpc.v1.ConditionOperator operator = 1;
        Field field = 2;
        cloudbeds.type.v1.Value value = 3;
    }
}


message ListContactByIdRequest {
    int64 id = 1;
}

message ListContactByIdResponse {
    GroupProfileContact contact = 1;
}