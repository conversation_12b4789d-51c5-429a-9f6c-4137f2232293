syntax = "proto3";

package cloudbeds.resource.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/cloudbeds/protos-go/resource/v1;resource";
option java_package = "com.cloudbeds.resource.v1";
option java_outer_classname = "ResourceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Resource\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Resource\\V1\\GPBMetadata";

message ResourceType {
  int64 id = 1;
  int64 property_id = 2;
  bool enabled = 3;
  string title = 4;
  string short_title = 5;
  string description = 6;
  bool has_availability = 7;
  int32 max_occupancy = 8;
  string event_interval_period = 9;
  int32 event_interval_amount = 10;
  int32 event_interval_min = 11;
  int32 event_interval_max = 12;
  string primary_image_url = 13;
  google.type.Money base_price = 14;
  string base_price_interval_type = 15;
  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 17;
  google.protobuf.Timestamp deleted_at = 18;
}

message Resource {
  int64 id = 1;
  int64 property_id = 2;
  int64 resource_type_id = 3;
  string title = 4;
  string description = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
}

message ReservationQuote {
  int64 id = 1;
  int64 property_id = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  int64 resource_id = 5;
  int64 resource_type_id = 6;
  int64 resource_reservation_id = 7;
  google.protobuf.Timestamp start_at = 8;
  google.protobuf.Timestamp end_at = 9;
  int32 adults = 10;
  int32 children = 11;
  string title = 12;
  string notes = 13;
  string house_account_id = 14;
  string guest_id = 15;
  string reservation_id = 16;
  string group_id = 17;
  string group_code = 18;
  string group_allotment_block_id = 19;
  string event_interval_period = 20;
  int32 event_interval_amount = 21;
  google.type.Money interval_rate = 22;
  int32 interval_quantity = 23;
  string interval_type = 24;
  google.type.Money net_amount = 25;
  google.type.Money inclusive_taxes_and_fees_amount = 26;
  google.type.Money subtotal = 27;
  google.type.Money exclusive_taxes_and_fees_amount = 28;
  google.type.Money total = 29;
  string currency_code = 30;

  repeated ReservationQuoteTaxesAndFee taxes_and_fees = 32;
}

message ReservationQuoteTaxesAndFee {
  int64 id = 1;
  int64 property_id = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  int64 reservation_quote_id = 5;
  string type = 6;
  string fee_id = 7;
  string tax_id = 8;
  string name = 9;
  string code = 10;
  string amount = 11;
  string amount_adult = 12;
  string amount_child = 13;
  string amount_type = 14;
  string amount_rate_based = 15;
  string inclusive_or_exclusive = 16;
  string available_for = 17;
  string fees_charged = 18;
  google.type.Money total_amount = 19;
  string parent_kind = 20;
  string parent_id = 21;
}

message ResourceReservation {
  int64 id = 1;
  int64 property_id = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  int64 resource_id = 5;
  int64 resource_type_id = 6;
  google.protobuf.Timestamp start_at = 7;
  google.protobuf.Timestamp end_at = 8;
  int32 adults = 9;
  int32 children = 10;
  string title = 11;
  string notes = 12;
  string house_account_id = 13;
  string guest_id = 14;
  string reservation_id = 15;
  string group_id = 16;
  string group_code = 17;
  string group_allotment_block_id = 18;
  string event_interval_period = 19;
  int32 event_interval_amount = 20;
  google.type.Money interval_rate = 21;
  int32 interval_quantity = 22;
  string interval_type = 23;
  google.type.Money net_amount = 24;
  google.type.Money inclusive_taxes_and_fees_amount = 25;
  google.type.Money subtotal = 26;
  google.type.Money exclusive_taxes_and_fees_amount = 27;
  google.type.Money total = 28;
  string currency_code = 29;
  string status = 30;
  google.protobuf.Timestamp canceled_at = 31;
  string confirmation_code = 32;

  repeated ResourceReservationTaxesAndFee taxes_and_fees = 33;
}

message ResourceReservationTaxesAndFee {
  int64 id = 1;
  int64 property_id = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  int64 resource_reservation_id = 5;
  string type = 6;
  string fee_id = 7;
  string tax_id = 8;
  string name = 9;
  string code = 10;
  string amount = 11;
  string amount_adult = 12;
  string amount_child = 13;
  string amount_type = 14;
  string amount_rate_based = 15;
  string inclusive_or_exclusive = 16;
  string available_for = 17;
  string fees_charged = 18;
  google.type.Money total_amount = 19;
  string parent_kind = 20;
  string parent_id = 21;
}

message Event {
  int64 id = 1;
  int64 property_id = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  string type = 5;
  string title = 6;
  string reason = 7;
  google.protobuf.Timestamp start_at = 8;
  google.protobuf.Timestamp end_at = 9;
  int64 resource_id = 10;
  int64 resource_type_id = 11;
  int64 resource_reservation_id = 12;
}