syntax = "proto3";

package cloudbeds.resource.v1;

import "cloudbeds/resource/v1/resource.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/resource/v1;resource";
option java_package = "com.cloudbeds.resource.v1";
option java_outer_classname = "ResourceEventsProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Resource\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Resource\\V1\\GPBMetadata";

enum ResourceTypeEventType {
  RESOURCE_TYPE_EVENT_TYPE_UNSPECIFIED = 0;
  RESOURCE_TYPE_EVENT_TYPE_CREATED = 1;
  RESOURCE_TYPE_EVENT_TYPE_UPDATED = 2;
  RESOURCE_TYPE_EVENT_TYPE_DELETED = 3;
}

message ResourceTypeEvent {
  string id = 1;
  ResourceTypeEventType type = 2;
  string event_source = 3;
  google.protobuf.Timestamp event_timestamp = 4;
  ResourceType payload = 5;
}

enum ResourceEventType {
  RESOURCE_EVENT_TYPE_UNSPECIFIED = 0;
  RESOURCE_EVENT_TYPE_CREATED = 1;
  RESOURCE_EVENT_TYPE_UPDATED = 2;
  RESOURCE_EVENT_TYPE_DELETED = 3;
}

message ResourceEvent {
  string id = 1;
  ResourceEventType type = 2;
  string event_source = 3;
  google.protobuf.Timestamp event_timestamp = 4;
  Resource payload = 5;
}

enum ReservationQuoteEventType {
  RESERVATION_QUOTE_EVENT_TYPE_UNSPECIFIED = 0;
  RESERVATION_QUOTE_EVENT_TYPE_CREATED = 1;
  RESERVATION_QUOTE_EVENT_TYPE_UPDATED = 2;
  RESERVATION_QUOTE_EVENT_TYPE_DELETED = 3;
  RESERVATION_QUOTE_EVENT_TYPE_CONFIRMED = 4;
}

message ReservationQuoteEvent {
  string id = 1;
  ReservationQuoteEventType type = 2;
  string event_source = 3;
  google.protobuf.Timestamp event_timestamp = 4;
  ReservationQuote payload = 5;
}

enum ResourceReservationEventType {
  RESOURCE_RESERVATION_EVENT_TYPE_UNSPECIFIED = 0;
  RESOURCE_RESERVATION_EVENT_TYPE_CREATED = 1;
  RESOURCE_RESERVATION_EVENT_TYPE_UPDATED = 2;
  RESOURCE_RESERVATION_EVENT_TYPE_CANCELLED = 3;
}

message ResourceReservationEvent {
  string id = 1;
  ResourceReservationEventType type = 2;
  string event_source = 3;
  google.protobuf.Timestamp event_timestamp = 4;
  ResourceReservation payload = 5;
}

enum ResourceEventEventType {
  RESOURCE_EVENT_EVENT_TYPE_UNSPECIFIED = 0;
  RESOURCE_EVENT_EVENT_TYPE_CREATED = 1;
  RESOURCE_EVENT_EVENT_TYPE_UPDATED = 2;
  RESOURCE_EVENT_EVENT_TYPE_DELETED = 3;
}

message ResourceEventEvent {
  string id = 1;
  ResourceEventEventType type = 2;
  string event_source = 3;
  google.protobuf.Timestamp event_timestamp = 4;
  Event payload = 5;
}