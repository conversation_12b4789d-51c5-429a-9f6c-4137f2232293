syntax = "proto3";

package cloudbeds.amcs.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/amcs/v1;amcs";
option java_package = "com.cloudbeds.amcs.v1";
option java_outer_classname = "GhcPerformanceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Amcs\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Amcs\\V1\\GPBMetadata";

message GhcPerformance {
  string id = 1;
  string property_id = 2;
  google.protobuf.Timestamp date = 3;
  int64 impressions = 4;
  int64 clicks = 5;
  double click_through_rate = 6;
  google.protobuf.Timestamp created_at = 7;
  optional google.protobuf.Timestamp updated_at = 8;
}