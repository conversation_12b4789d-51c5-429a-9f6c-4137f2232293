syntax = "proto3";

package cloudbeds.amcs.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/cloudbeds/protos-go/amcs/v1;amcs";
option java_package = "com.cloudbeds.amcs.v1";
option java_outer_classname = "GooglePmaxCampaignProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Amcs\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Amcs\\V1\\GPBMetadata";

message GooglePmaxCampaign {
  string id = 1;
  int32 company_id = 2;
  int64 property_id = 3;
  optional double daily_budget = 4;
  string visibility = 5;
  string status = 6;
  optional double monthly_fee = 7;
  optional string property_currency = 8;
  string campaign_id = 9;
  string metasearch_reference = 10;
  optional double bid_value = 11;
  optional google.protobuf.Timestamp created_at = 12;
  optional google.protobuf.Timestamp updated_at = 13;
}