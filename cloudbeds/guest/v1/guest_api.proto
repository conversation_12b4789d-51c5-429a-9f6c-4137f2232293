syntax = "proto3";

package cloudbeds.guest.v1;

option go_package = "github.com/cloudbeds/protos-go/guest/v1;guest";
option java_package = "com.cloudbeds.guest.v1";
option java_outer_classname = "GuestProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Guest\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Guest\\V1\\GPBMetadata";

import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "cloudbeds/rpc/v1/common.proto";
import "cloudbeds/type/v1/struct.proto";
import "cloudbeds/guest/v1/common.proto";

service GuestService {
  rpc ListPersons (ListPersonsRequest) returns (ListPersonsResponse);
  rpc GetPerson (GetPersonRequest) returns (GetPersonResponse);
  rpc GetPersonByExternalId (GetPersonByExternalIdRequest) returns (GetPersonByExternalIdResponse);
  rpc CreatePerson (CreatePersonRequest) returns (CreatePersonResponse);
  rpc CreatePersons (CreatePersonsRequest) returns (CreatePersonsResponse);
  rpc UpdatePerson (UpdatePersonRequest) returns (UpdatePersonResponse);
  rpc DeletePerson (DeletePersonRequest) returns (DeletePersonResponse);
  rpc AnonymizePerson (AnonymizePersonRequest) returns (AnonymizePersonResponse);
  rpc GetFilterValues (GetFilterValuesRequest) returns (GetFilterValuesResponse);
  rpc ListExternalIdsByPersonId (ListExternalIdsByPersonIdRequest) returns (ListExternalIdsByPersonIdResponse);
  rpc ListMerges (ListMergesRequest) returns (ListMergesResponse);
  rpc MergePersons (MergePersonsRequest) returns (MergePersonsResponse);
  rpc ListSuggestions (ListSuggestionsRequest) returns (ListSuggestionsResponse);
  rpc UnmergePersons (UnmergePersonsRequest) returns (UnmergePersonsResponse);
}

message ListSuggestionsRequest {
  uint64 organization_id = 1;
  optional uint64 person_id = 2;
  optional Filter filter = 3;
  repeated OrderBy order_by = 4;
  optional string page_token = 5;
  optional int32 page_size = 6;

  message OrderBy {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_SUGGESTION_ID = 1;
      FIELD_CREATED_AT = 2;
    }

    Field field = 1;
    cloudbeds.rpc.v1.Direction direction = 2;
  }

  message Filter {
    oneof kind {
      FieldFilter field_filter = 1;
    }
  }

  message FieldFilter {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_PERSON_ID = 1;
      FIELD_ORGANIZATION_ID = 2;
      FIELD_SUGGESTION_ID = 3;
      FIELD_CREATED_AT = 4;
    }

    cloudbeds.rpc.v1.ConditionOperator operator = 1;
    Field field = 2;
    cloudbeds.type.v1.Value value = 3;
  }
}

message ListSuggestionsResponse {
  repeated Suggestion suggestions = 1;
  optional string next_page_token = 2;
  optional int32 total_size = 3;
}

message CreatePersonRequest {
  uint64 organization_id = 1;
  ContactDetails contact_details = 2;
  Address address = 3;
  Document document = 4;
  TaxInfo tax_info = 5;
  optional bool is_opt_in_marketing = 6;
  optional uint64 external_id = 7;
}

message CreatePersonResponse {
  Person person = 1;
}

message CreatePersonsRequest {
  repeated CreatePersonRequest persons = 1;
}

message CreatePersonsResult {
  uint32 operation_index = 1;
  oneof data {
    CreatePersonResponse create_person_response = 2;
    google.rpc.Status status = 3;
  }
}

message CreatePersonsResponse {
  repeated CreatePersonsResult results = 1;
}

message UpdatePersonRequest {
  uint64 id = 1;
  uint64 organization_id = 2;
  optional uint64 version_id = 3;
  ContactDetails contact_details = 4;
  Address address = 5;
  Document document = 6;
  TaxInfo tax_info = 7;
  optional bool is_opt_in_marketing = 8;
  optional bool set_as_current_version = 9;
  optional uint64 external_id = 10;
}

message UpdatePersonResponse {
  Person person = 1;
}

message DeletePersonRequest {
  uint64 id = 1;
  uint64 organization_id = 2;
}

message DeletePersonResponse {
  bool success = 1;
}

message GetPersonRequest {
  uint64 id = 1;
  uint64 organization_id = 2;
  optional uint64 version_id = 3;
}

message GetPersonResponse {
  Person person = 1;
}

message ListPersonsRequest {
  message OrderBy {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_EMAIL = 1;
      FIELD_PHONE = 2;
      FIELD_FIRST_NAME = 3;
      FIELD_LAST_NAME = 4;
      FIELD_ID = 5;
      FIELD_COUNTRY = 6;
      FIELD_CREATED_AT = 7;
      FIELD_UPDATED_AT = 8;
      FIELD_IS_MERGED = 9;
    }

    Field field = 1;
    cloudbeds.rpc.v1.Direction direction = 2;
  }

  message Filter {
    oneof kind {
      CompositeFilter composite_filter = 1;
      FieldFilter field_filter = 2;
    }
  }

  message CompositeFilter {
    cloudbeds.rpc.v1.LogicalOperator operator = 1;
    repeated Filter filters = 2;
  }

  message FieldFilter {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_EMAIL = 1;
      FIELD_PHONE = 2;
      FIELD_FIRST_NAME = 3;
      FIELD_LAST_NAME = 4;
      FIELD_ID = 5;
      FIELD_COUNTRY = 6;
      FIELD_STATUS_ID = 7;
      FIELD_CREATED_AT = 8;
      FIELD_UPDATED_AT = 9;
      FIELD_CELL_PHONE = 10;
      FIELD_IS_MERGED = 11;
      FIELD_PARENT_ID = 12;
      FIELD_CHECK_IN_DATE = 13;
      FIELD_CHECK_OUT_DATE = 14;
      FIELD_EXTERNAL_ID = 15;
    }

    cloudbeds.rpc.v1.ConditionOperator operator = 1;
    Field field = 2;
    cloudbeds.type.v1.Value value = 3;
  }

  int64 organization_id = 1;
  optional cloudbeds.guest.v1.Filter filter = 2;

  // Token of the page to retrieve. If not specified, the first
  // page of results will be returned. Use the value obtained from
  // `next_page_token` in the previous response in order to request
  // the next page of results.
  optional string page_token = 3;

  // Number of elements to retrieve in a single page.
  optional int32 page_size = 4;

  repeated cloudbeds.guest.v1.OrderBy order_by = 5;
  optional Filter new_filter = 6;
  repeated OrderBy new_order_by = 7;
}

message ListPersonsResponse {
  repeated Person persons = 1;

  optional int32 next_page = 2;
  optional int32 total_size = 3;

  // Pagination token used to retrieve the next page of results.
  // Pass the content of this string as the `page_token` attribute of the next request.
  // `next_page_token` is not returned for the last page.
  optional string next_page_token = 4;
}

message GetFilterValuesRequest {
  uint64 organization_id = 1;
}

message GetFilterValuesResponse {
  repeated string countries = 1;
  repeated uint64 statuses = 2;
}

message AnonymizePersonRequest {
  uint64 id = 1;
  uint64 organization_id = 2;
}

message AnonymizePersonResponse {
  Person person = 1;
}

message GetPersonByExternalIdRequest {
  uint64 external_id = 1;
  uint64 organization_id = 2;
}

message GetPersonByExternalIdResponse {
  Person person = 1;
}

message ListExternalIdsByPersonIdRequest {
  uint64 person_id = 1;
  uint64 organization_id = 2;
}

message ListExternalIdsByPersonIdResponse {
  repeated uint64 external_ids = 1;
}

message ListMergesRequest {
  message OrderBy {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_ID = 1;
      FIELD_SUGGESTION_ID = 2;
      FIELD_PERSON_ID = 3;
      FIELD_TYPE = 4;
      FIELD_CREATED_AT = 5;
      FIELD_REVERTED_AT = 6;
    }

    Field field = 1;
    cloudbeds.rpc.v1.Direction direction = 2;
  }

  message Filter {
    oneof kind {
      CompositeFilter composite_filter = 1;
      FieldFilter field_filter = 2;
    }
  }

  message CompositeFilter {
    cloudbeds.rpc.v1.LogicalOperator operator = 1;
    repeated Filter filters = 2;
  }

  message FieldFilter {
    enum Field {
      FIELD_UNSPECIFIED = 0;
      FIELD_ID = 1;
      FIELD_SUGGESTION_ID = 2;
      FIELD_PERSON_ID = 3;
      FIELD_TYPE = 4;
      FIELD_CREATED_AT = 5;
      FIELD_REVERTED_AT = 6;
    }

    cloudbeds.rpc.v1.ConditionOperator operator = 1;
    Field field = 2;
    cloudbeds.type.v1.Value value = 3;
  }

  uint64 organization_id = 1;
  optional google.protobuf.Timestamp since = 2;
  optional int32 page = 3 ;

  // Number of elements to retrieve in a single page.
  optional int32 page_size = 4;

  optional Filter filter = 5;
  repeated OrderBy order_by = 6;

  // Token of the page to retrieve. If not specified, the first
  // page of results will be returned. Use the value obtained from
  // `next_page_token` in the previous response in order to request
  // the next page of results.
  optional string page_token = 7;
}

message ListMergesResponse {
  repeated PersonMerge person_merges = 1;
  optional int32 next_page = 2;
  optional int32 total_size = 3;

  // Pagination token used to retrieve the next page of results.
  // Pass the content of this string as the `page_token` attribute of the next request.
  // `next_page_token` is not returned for the last page.
  optional string next_page_token = 4;
}

message MergePersonsRequest {
  uint64 organization_id = 1;
  repeated uint64 persons_ids_to_merge = 2;
  ContactDetails contact_details = 3;
  Address address = 4;
  Document document = 5;
  TaxInfo tax_info = 6;
  optional bool is_opt_in_marketing = 7;
}

message MergePersonsResponse {
  PersonMerge person_merge = 1;
}

message UnmergePersonsRequest {
  uint64 organization_id = 1;
  uint64 merge_id = 2;
}

message UnmergePersonsResponse {
  PersonMerge person_unmerge = 1;
}
