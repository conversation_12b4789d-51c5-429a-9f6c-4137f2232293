syntax = "proto3";

package cloudbeds.guest.v1;

option go_package = "github.com/cloudbeds/protos-go/guest/v1;guest";
option java_package = "com.cloudbeds.guest.v1";
option java_outer_classname = "CommonProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Guest\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Guest\\V1\\GPBMetadata";

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "cloudbeds/rpc/v1/common.proto";

enum Gender {
  GENDER_UNSPECIFIED = 0;
  GENDER_M = 1;
  GENDER_F = 2;
}

enum DocumentType {
  DOCUMENT_TYPE_UNSPECIFIED = 0;
  DOCUMENT_TYPE_DRIVER_LICENSE = 1;
  DOCUMENT_TYPE_SOCIAL_SECURITY_CARD = 2;
  DOCUMENT_TYPE_STUDENT_ID = 3;
  DOCUMENT_TYPE_PASSPORT = 4;
  DOCUMENT_TYPE_DNI = 5;
  DOCUMENT_TYPE_NIE = 6;
}

message ContactDetails {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string phone = 4;
  Gender gender = 5;
  string cell_phone = 6;
  optional google.protobuf.Timestamp birthday = 7;
  optional uint64 photo_id = 8;
  optional uint64 status_id = 9;
  string email_hash = 10;
}

message Address {
  string city = 1;
  string state = 2;
  string zip = 3;
  string country = 4;
  string address1 = 5;
  string address2 = 6;
}

message Document {
  DocumentType type = 1;
  optional string number = 2;
  optional string issuing_country = 3;
  optional google.protobuf.Timestamp issue_date = 4;
  optional google.protobuf.Timestamp expiration_date = 5;
}

message TaxInfo {
  string guest_tax_id_number = 1;
  string company_name = 2;
  string company_tax_id_number = 3;
}

message PersonVersion {
  uint64 id = 1;
  uint64 organization_id = 2;
  uint64 person_id = 3;

  ContactDetails contact_details = 4;
  Address address = 5;
  Document document = 6;
  TaxInfo tax_info = 7;
  uint64 user_id = 8;

  bool is_opt_in_marketing = 9;
  string opt_in_marketing_hash = 10;

  google.protobuf.Timestamp created_at = 11;
}

message Person {
  uint64 id = 1;
  uint64 organization_id = 2;
  PersonVersion current_version = 3;
  optional Person parent = 4;

  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  optional google.protobuf.Timestamp deleted_at = 7;
  optional google.protobuf.Timestamp anonymized_at = 8;
  repeated uint64 external_ids = 9;
}

message OrderBy {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_EMAIL = 1;
    FIELD_PHONE = 2;
    FIELD_FIRST_NAME = 3;
    FIELD_LAST_NAME = 4;
    FIELD_ID = 5;
    FIELD_COUNTRY = 6;
    FIELD_CREATED_AT = 7;
    FIELD_LAST_CHANGE = 8;
    FIELD_IS_REPEAT_GUEST = 9;
  }

  Field field = 1;
  cloudbeds.rpc.v1.Direction direction = 2;
}

message Filter {
  oneof kind {
    CompositeFilter composite_filter = 1;
    FieldFilter field_filter = 2;
  }
}

message CompositeFilter {
  cloudbeds.rpc.v1.LogicalOperator operator = 1;
  repeated Filter filters = 2;
}

message FieldFilter {
  enum Field {
    FIELD_UNSPECIFIED = 0;
    FIELD_EMAIL = 1;
    FIELD_PHONE = 2;
    FIELD_FIRST_NAME = 3;
    FIELD_LAST_NAME = 4;
    FIELD_ID = 5;
    FIELD_COUNTRY = 6;
    FIELD_STATUS_ID = 7;
    FIELD_CREATED_AT = 8;
    FIELD_LAST_CHANGE = 9;
    FIELD_CELL_PHONE = 10;
    FIELD_IS_REPEAT_GUEST = 11;
    FIELD_PARENT_ID = 12;
    FIELD_CHECK_IN_DATE = 13;
    FIELD_CHECK_OUT_DATE = 14;
    FIELD_EXTERNAL_ID = 15;
  }

  cloudbeds.rpc.v1.ConditionOperator operator = 1;
  Field field = 2;
  google.protobuf.Value value = 3;
}

message Suggestion {
  uint64 id = 1;
  uint64 organization_id = 2;
  repeated SuggestionPerson suggestion_persons = 4;

  google.protobuf.Timestamp created_at = 3;
}

message SuggestionPerson {
  uint64 id = 1;
  uint64 suggestion_id = 2;
  uint64 person_id = 3;
}

/**
 * PersonMerge represents the results of either a merge or unmerge operation.
 *
 * For merges, the `before` property will contain the full list of people involved in the merge.
 * The `after` property will contain the single new person after merge.
 *
 * For unmerges, the `before` property will contain the person to unlink from a prior merge.
 * The `after` property will contain the list of people after the unmerge operation is complete.
 */
message PersonMerge {
  enum EventType {
    EVENT_TYPE_UNSPECIFIED = 0;
    EVENT_TYPE_MERGE = 1;
    EVENT_TYPE_UNMERGE = 2;
  }
  uint64 id = 1;
  EventType event_type = 2;
  repeated Person before = 3;
  repeated Person after = 4;
  google.protobuf.Timestamp event_date = 5;
}
