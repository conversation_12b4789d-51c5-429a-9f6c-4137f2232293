syntax = "proto3";

package cloudbeds.marketplace.v1;

option go_package = "github.com/cloudbeds/protos-go/marketplace/v1;marketplace";
option java_package = "com.cloudbeds.marketplace.v1";
option java_outer_classname = "MarketplaceServiceProto";
option java_multiple_files = true;
option php_namespace = "Cloudbeds\\Protos\\Marketplace\\V1";
option php_metadata_namespace = "Cloudbeds\\Protos\\Marketplace\\V1\\GPBMetadata";

service MarketplaceService {
  rpc GetInvoiceIntegrationEnabled(GetInvoiceIntegrationEnabledRequest) returns (GetInvoiceIntegrationEnabledResponse);
}

message GetInvoiceIntegrationEnabledRequest {
  int64 property_id = 1;
}

message GetInvoiceIntegrationEnabledResponse {
  bool enabled = 1;
}
