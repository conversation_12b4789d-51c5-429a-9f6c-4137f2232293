# Central Protos 

This project aims to serve as the single source-of-truth for protobuf data contracts at Cloudbeds. Contributions to this project result in the automatic code generation of protobuf message and gRPC stubs in various target languages.

## Dependencies

* Buf (https://docs.buf.build/installation)

## Intialize a new package

```
bash scripts/init-package.sh booking
```

## Style Guide / Linting

We follow [B<PERSON>'s Style Guide](https://docs.buf.build/format/style) for protobufs and have <PERSON><PERSON>'s [linter](https://docs.buf.build/lint/overview) configured on the `DEFAULT` level.

The linter uses the configuration specified in `buf.yaml`.

We also provide a `pre-commit` hook to catch any linting issues locally. To install, simply run:

```
cp .githooks/pre-commit .git/hooks/pre-commit
```

## Breaking Changes

One of the core promises of Protobuf is forwards and backwards compatibility. But making sure that your Protobuf schema doesn't introduce breaking changes isn't automatic - there are rules you need to follow to ensure that your schema remains compatible for its lifetime.

While our package structure supports versioning, it should only be done as a last resort. 

Following a [simple set of rules](https://developers.google.com/protocol-buffers/docs/proto#updating) allows for one to release changes to protobuf definitions in a forwards and backwards compatible way. We also utilize the `buf breaking` command on a per-file basis (`FILE` here https://docs.buf.build/breaking/overview) to catch potential BC breaks in PRs.

## Code Generation

We automate code generation of gRPC source code for multiple languages. The exact process differs per language, but they all follow the same general flow:

* Publish versioned tag (semv) to `github.com/cloudbeds/protos-${lang}` repository on commit to master
* Publish SNAPSHOT/dev artifacts (and/or tags) on any other branch for use in development.

Code generation is handled by the `publish` Github action.

Please refer to the language specific repositories for further information on how to use the generated code:

* [Java](https://github.com/cloudbeds/protos-java)
* [Go](https://github.com/cloudbeds/protos-go)
* [PHP](https://github.com/cloudbeds/protos-php)
* [Python](https://github.com/cloudbeds/protos-python)

### Local Generation

First, [install Buf](https://docs.buf.build/installation) ...

Generate all
```
make gen-sources
```

Generate docs
```
make gen-docs
```

Generate a specific service
```
buf generate --path cloudbeds/policy --template buf.gen.yaml
```

Generated sources are located in `gen/${lang}`.

## Versioning

There are two different versioning schemes in play in this repository.

First, there is the versioning of protobuf packages as seen in the path: `cloudbeds/policy/v1/policy.proto`. This version is locked into place and should never change. Any protobufs contained within this package are checked for #Breaking-Changes.

Second, there is the versioning of the generated sources which you can read more about [here](https://github.com/cloudbeds/protos/wiki/How-the-version-works-in-proto-and-deployment).

## Protos Documentation

Documentation is generated automatically after merging to `master` and pushed to Github pages. To view the most recent docs for your changes, you may run `make gen-docs`.

Current docs for the `master` branch are here: https://fictional-waffle-32e39b85.pages.github.io.

## Adding Support for a new language

1. Create the language specific repository following the pattern: `protos-{lang}`. Ex: `github.com/cloudbeds/protos-python`
2. Add the `cloudbeds-ci` user with `write` access to the new repository
3. Open a PR to this repository with the following:
   1. Bump the root version (to ensure we publish some initial sources for the language)
   2. A new github action to handle publishing (For example, see https://github.com/cloudbeds/protos/blob/a52d494e9ee23ab425244c746453fd70baedcbb5/.github/actions/publish/php/action.yaml for PHP)
   3. Update the [Buf Generate](https://github.com/cloudbeds/protos/blob/master/buf.gen.yaml) config for the new language
   4. Enable the new action in the [publish.yaml](https://github.com/cloudbeds/protos/blob/master/.github/workflows/publish.yaml) workflow
   5. (Optional) Validate your changes by pulling down the develop branch of the published artifacts
4. Once closed, remember to update the `README` of the language repository with instructions of how to use the project.
